package com.leapmotor.sdl.framework.security.filter;

import com.alibaba.fastjson2.JSON;
import com.leapmotor.sdl.common.core.domain.AjaxResult;
import com.leapmotor.sdl.common.core.domain.OpenApiAccessLog;
import com.leapmotor.sdl.common.core.domain.OpenApiKey;
import com.leapmotor.sdl.common.core.redis.RedisCache;
import com.leapmotor.sdl.common.utils.StringUtils;
import com.leapmotor.sdl.common.utils.ip.IpUtils;
import com.leapmotor.sdl.common.utils.openapi.OpenApiUtils;
import com.leapmotor.sdl.common.utils.spring.SpringUtils;
import com.leapmotor.sdl.system.service.IOpenApiKeyService;
import com.leapmotor.sdl.system.service.IOpenApiAccessLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.annotation.PostConstruct;
import jakarta.servlet.*;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * OpenAPI访问过滤器
 * 
 * 用于验证OpenAPI接口的访问权限，包括：
 * 1. API Key验证
 * 2. 权限组验证（暂无）
 * 3. IP白名单验证
 * 4. 访问频率限制
 * 5. 访问日志记录
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Component
@Order(2)
public class OpenApiFilter extends OncePerRequestFilter
{
    private static final Logger log = LoggerFactory.getLogger(OpenApiFilter.class);
    
    /** API Key请求头名称 */
    private static final String API_KEY_HEADER = "X-API-Key";
    
    /** 默认限流时间窗口（小时） */
    private static final int DEFAULT_RATE_LIMIT_HOURS = 1;
    
    @Autowired
    private RedisCache redisCache;
    
    /**
     * OpenAPI密钥服务
     */
    private IOpenApiKeyService openApiKeyService;
    
    /**
     * OpenAPI访问日志服务
     */
    private IOpenApiAccessLogService openApiAccessLogService;
    
    @PostConstruct
    public void init() 
    {
        log.info("对外API过滤器初始化完成");
        // 初始化服务实例
        try 
        {
            openApiKeyService = SpringUtils.getBean(IOpenApiKeyService.class);
            openApiAccessLogService = SpringUtils.getBean(IOpenApiAccessLogService.class);
        } 
        catch (Exception e) 
        {
            log.warn("初始化OpenAPI服务时发生错误: {}", e.getMessage());
        }
    }
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) 
            throws ServletException, IOException 
    {
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        
        try 
        {
            // 检查是否为OpenAPI请求
            if (!isOpenApiRequest(request)) 
            {
                // 非OpenAPI请求，直接放行
                chain.doFilter(request, response);
                return;
            }
            
            String apiKey = request.getHeader(API_KEY_HEADER);
            if (StringUtils.isEmpty(apiKey)) 
            {
                writeErrorResponse(response, "缺少API Key", HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }
            
            // 验证API Key
            OpenApiKey openApiKey = validateApiKey(apiKey);
            if (openApiKey == null) 
            {
                writeErrorResponse(response, "无效的API Key", HttpServletResponse.SC_UNAUTHORIZED);
                recordAccessLog(apiKey, request, HttpServletResponse.SC_UNAUTHORIZED, 
                    System.currentTimeMillis() - startTime, "无效的API Key");
                return;
            }
            
            // 验证权限组，有空再补充
//            String requestGroup = getRequestGroup(request);
//            if (!OpenApiUtils.isGroupAllowed(requestGroup, openApiKey.getAllowedGroups()))
//            {
//                writeErrorResponse(response, "无权限访问该接口", HttpServletResponse.SC_FORBIDDEN);
//                recordAccessLog(apiKey, request, HttpServletResponse.SC_FORBIDDEN,
//                    System.currentTimeMillis() - startTime, "无权限访问该接口");
//                return;
//            }
            
            // 验证IP白名单
            String clientIp = IpUtils.getIpAddr(request);
            if (!OpenApiUtils.isIpInWhitelist(clientIp, openApiKey.getIpWhitelist())) 
            {
                writeErrorResponse(response, "IP地址不在白名单中", HttpServletResponse.SC_FORBIDDEN);
                recordAccessLog(apiKey, request, HttpServletResponse.SC_FORBIDDEN, 
                    System.currentTimeMillis() - startTime, "IP地址不在白名单中");
                return;
            }
            
            // 验证访问频率限制
            if (!checkRateLimit(apiKey, openApiKey.getRateLimit()))
            {
                writeErrorResponse(response, "访问频率超限", 429);
                recordAccessLog(apiKey, request, 429,
                    System.currentTimeMillis() - startTime, "访问频率超限");
                return;
            }

            // 更新API Key使用信息
            updateApiKeyUsage(apiKey);
            
            chain.doFilter(request, response);
            
            recordAccessLog(apiKey, request, HttpServletResponse.SC_OK,
                System.currentTimeMillis() - startTime, null);
            
        } 
        catch (Exception e) 
        {
            log.error("OpenAPI过滤器处理请求时发生错误: {}", e.getMessage(), e);
            writeErrorResponse(response, "系统内部错误", HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
    
    /**
     * 检查是否为OpenAPI请求
     * 
     * 通过检查请求路径是否以"/api/"开头来判断，有时间再改成通过拦截器实现：HandlerInterceptor判断注解是否是@OpenAPI
     * 
     * @param request HTTP请求
     * @return 是否为OpenAPI请求
     */
    private boolean isOpenApiRequest(HttpServletRequest request) 
    {
        try 
        {
            String requestPath = request.getRequestURI();
            return requestPath.startsWith("/api/");
        } 
        catch (Exception e) 
        {
            log.warn("检查OpenAPI请求时发生错误: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 验证API Key是否有效
     * 
     * @param apiKey API密钥
     * @return OpenApiKey对象，如果无效则返回null
     */
    private OpenApiKey validateApiKey(String apiKey) 
    {
        try 
        {
            if (openApiKeyService == null) 
            {
                log.warn("OpenAPI密钥服务未初始化");
                return null;
            }
            
            // 调用服务层方法验证API Key
            OpenApiKey openApiKey = openApiKeyService.selectOpenApiKeyByApiKey(apiKey);
            
            if (openApiKey == null) 
            {
                return null; // 密钥不存在
            }
            
            // 检查状态
            if (!"0".equals(openApiKey.getStatus())) 
            {
                return null; // 密钥已停用
            }
            
            // 检查过期时间
            if (openApiKey.getExpireTime() != null && 
                openApiKey.getExpireTime().before(new Date())) 
            {
                return null; // 密钥已过期
            }
            
            return openApiKey;
            
        } 
        catch (Exception e) 
        {
            log.error("验证API Key时发生错误: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 获取请求的权限组
     * 
     * @param request HTTP请求
     * @return 权限组名称
     */
    private String getRequestGroup(HttpServletRequest request) 
    {
        try 
        {
            // 这里可以通过请求路径或其他方式获取权限组
            // 实际实现时需要根据Controller的@OpenAPI注解获取
            String requestPath = request.getRequestURI();

            // 简单的路径映射逻辑
            if (requestPath.contains("/user"))
            {
                return "user";
            }
            else if (requestPath.contains("/order"))
            {
                return "order";
            }
            else if (requestPath.contains("/product"))
            {
                return "product";
            }

            return "default";
        } 
        catch (Exception e) 
        {
            log.warn("获取请求权限组时发生错误: {}", e.getMessage());
            return "default";
        }
    }
    
    /**
     * 检查访问频率限制
     * 
     * 使用Redis实现基于令牌桶算法的限流
     * 
     * @param apiKey API密钥
     * @param rateLimit 限流次数
     * @return 是否允许访问
     */
    private boolean checkRateLimit(String apiKey, Integer rateLimit) 
    {
        try 
        {
            if (rateLimit == null || rateLimit <= 0) 
            {
                return true; // 没有限流配置
            }
            
            String rateLimitKey = OpenApiUtils.getRateLimitKey(apiKey);
            
            // 获取当前计数
            Integer currentCount = redisCache.getCacheObject(rateLimitKey);
            if (currentCount == null) 
            {
                currentCount = 0;
            }
            
            // 检查是否超过限制
            if (currentCount >= rateLimit) 
            {
                return false;
            }
            
            // 增加计数
            redisCache.setCacheObject(rateLimitKey, currentCount + 1, 
                DEFAULT_RATE_LIMIT_HOURS, TimeUnit.HOURS);
            
            return true;
        } 
        catch (Exception e) 
        {
            log.error("检查访问频率限制时发生错误: {}", e.getMessage(), e);
            return true; // 出错时允许访问
        }
    }
    
    /**
     * 更新API Key使用信息
     * 
     * @param apiKey API密钥
     */
    private void updateApiKeyUsage(String apiKey) 
    {
        try 
        {
            if (openApiKeyService != null) 
            {
                // 异步更新API Key使用信息
                openApiKeyService.updateApiKeyUsage(apiKey);
            }
        } 
        catch (Exception e) 
        {
            log.error("更新API Key使用信息时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 记录访问日志
     * 
     * @param apiKey API密钥
     * @param request HTTP请求
     * @param responseStatus 响应状态码
     * @param responseTime 响应时间
     * @param errorMessage 错误信息
     */
    private void recordAccessLog(String apiKey, HttpServletRequest request, 
                                int responseStatus, long responseTime, String errorMessage) 
    {
        try 
        {
            if (openApiAccessLogService != null) 
            {
                OpenApiAccessLog accessLog = new OpenApiAccessLog();
                accessLog.setApiKey(apiKey);
                accessLog.setRequestUri(request.getRequestURI());
                accessLog.setRequestMethod(request.getMethod());
                accessLog.setClientIp(IpUtils.getIpAddr(request));
                accessLog.setUserAgent(request.getHeader("User-Agent"));
                accessLog.setResponseStatus(responseStatus);
                accessLog.setResponseTime(responseTime);
                accessLog.setRequestTime(new Date());
                accessLog.setErrorMessage(errorMessage);
                
                // 异步记录访问日志
                openApiAccessLogService.recordAccessLogAsync(accessLog);
            }
        } 
        catch (Exception e) 
        {
            log.error("记录访问日志时发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 写入错误响应
     * 
     * @param response HTTP响应
     * @param message 错误消息
     * @param statusCode 状态码
     */
    private void writeErrorResponse(HttpServletResponse response, String message, int statusCode) 
    {
        try 
        {
            response.setStatus(statusCode);
            response.setContentType("application/json;charset=UTF-8");
            
            AjaxResult result = AjaxResult.error(message);
            response.getWriter().write(JSON.toJSONString(result));
        } 
        catch (IOException e) 
        {
            log.error("写入错误响应时发生错误: {}", e.getMessage(), e);
        }
    }
}