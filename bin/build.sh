#!/bin/bash

# SDL Platform 宿主机构建脚本
# 在宿主机上编译前后端代码

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "========================================="
echo "    SDL Platform 宿主机构建"
echo "========================================="

# 检查必要的工具
check_requirements() {
    log_info "检查构建环境..."
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        log_error "Java 未安装，请安装 JDK 17"
        exit 1
    fi
    
    java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [ "$java_version" -lt 17 ]; then
        log_error "需要 Java 17 或更高版本，当前版本: $java_version"
        exit 1
    fi
    log_success "Java 版本检查通过: $(java -version 2>&1 | head -n1)"
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        log_error "Maven 未安装，请安装 Maven 3.6+"
        exit 1
    fi
    log_success "Maven 版本: $(mvn -version | head -n1)"
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请安装 Node.js 16+"
        exit 1
    fi
    
    node_version=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$node_version" -lt 16 ]; then
        log_error "需要 Node.js 16 或更高版本，当前版本: $(node -v)"
        exit 1
    fi
    log_success "Node.js 版本检查通过: $(node -v)"
    
    # 检查pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm 未安装，正在安装..."
        if command -v npm &> /dev/null; then
            npm install -g pnpm --registry=https://registry.npmmirror.com
            log_success "pnpm 安装完成"
        else
            log_error "npm 未安装，无法安装 pnpm"
            exit 1
        fi
    fi
    log_success "pnpm 版本: $(pnpm -v)"
}

# 构建后端
build_backend() {
    log_info "开始构建后端..."
    
    # 清理之前的构建
    log_info "清理之前的构建..."
    mvn clean
    
    # 编译和打包
    log_info "编译和打包后端应用..."
    if mvn package -DskipTests; then
        log_success "后端构建成功"
        
        # 检查jar文件是否存在
        if [ -f "sdl-platform-admin/target/sdl-platform-admin.jar" ]; then
            log_success "后端jar文件生成成功: sdl-platform-admin/target/sdl-platform-admin.jar"
        else
            log_error "后端jar文件未找到"
            exit 1
        fi
    else
        log_error "后端构建失败"
        exit 1
    fi
}

# 构建前端
build_frontend() {
    log_info "开始构建前端..."

    cd sdl-platform-vue3

    # 清理缓存和旧的构建产物
    log_info "清理前端缓存和构建产物..."
    rm -rf node_modules/.cache 2>/dev/null || true
    rm -rf dist 2>/dev/null || true
    rm -rf .vite 2>/dev/null || true

    # 清理pnpm缓存
    log_info "清理pnpm缓存..."
    pnpm store prune 2>/dev/null || true

    # 设置pnpm镜像源
    log_info "配置pnpm镜像源..."
    pnpm config set registry https://registry.npmmirror.com

    # 安装依赖
    log_info "安装前端依赖..."
    if pnpm install; then
        log_success "前端依赖安装成功"
    else
        log_error "前端依赖安装失败"
        exit 1
    fi

    # 检查并安装缺失的依赖
    log_info "检查并安装缺失的依赖..."

    # 定义可能缺失的依赖列表
    missing_deps=()

    # 检查sortablejs依赖
    if ! pnpm list sortablejs >/dev/null 2>&1; then
        missing_deps+=("sortablejs")
    fi

    # 如果有缺失的依赖，批量安装
    if [ ${#missing_deps[@]} -gt 0 ]; then
        log_warning "检测到缺失的依赖: ${missing_deps[*]}"
        log_info "正在安装缺失的依赖..."
        if pnpm add "${missing_deps[@]}"; then
            log_success "缺失依赖安装成功"
        else
            log_error "缺失依赖安装失败"
            exit 1
        fi
    else
        log_success "所有依赖检查完成，无缺失依赖"
    fi

    # 构建生产版本
    log_info "构建前端生产版本..."
    if pnpm run build:prod; then
        log_success "前端构建成功"

        # 检查dist目录是否存在
        if [ -d "dist" ]; then
            log_success "前端构建产物生成成功: sdl-platform-vue3/dist/"
        else
            log_error "前端构建产物未找到"
            exit 1
        fi
    else
        log_error "前端构建失败"
        log_error "如果遇到依赖问题，请尝试手动安装："
        log_error "  cd sdl-platform-vue3"
        log_error "  pnpm add sortablejs"
        log_error "  pnpm run build:prod"
        exit 1
    fi

    cd ..
}

# 创建部署目录
prepare_deploy() {
    log_info "准备部署目录..."
    
    # 创建部署目录
    mkdir -p docker/deploy/backend
    mkdir -p docker/deploy/frontend
    
    # 复制后端jar文件
    cp sdl-platform-admin/target/sdl-platform-admin.jar docker/deploy/backend/
    log_success "后端文件复制完成"
    
    # 复制前端构建产物
    cp -r sdl-platform-vue3/dist/* docker/deploy/frontend/
    log_success "前端文件复制完成"
}

# 主函数
main() {
    check_requirements
    build_backend
    build_frontend
    prepare_deploy
    
    echo ""
    log_success "构建完成！"
    echo ""
    echo "构建产物："
    echo "  后端: docker/deploy/backend/sdl-platform-admin.jar"
    echo "  前端: docker/deploy/frontend/"
    echo ""
    echo "下一步："
    echo "  运行 ./deploy.sh 启动Docker容器"
    echo ""
}

# 执行主函数
main "$@"
