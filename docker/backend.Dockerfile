# SDL Platform 后端多阶段构建镜像
# 第一阶段：构建阶段
FROM maven:3.9-eclipse-temurin-17 AS builder

# 设置工作目录
WORKDIR /app

# 设置Maven镜像源为阿里云，提高下载速度
RUN mkdir -p /root/.m2 && \
    echo '<?xml version="1.0" encoding="UTF-8"?>' > /root/.m2/settings.xml && \
    echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"' >> /root/.m2/settings.xml && \
    echo '          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"' >> /root/.m2/settings.xml && \
    echo '          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0' >> /root/.m2/settings.xml && \
    echo '          http://maven.apache.org/xsd/settings-1.0.0.xsd">' >> /root/.m2/settings.xml && \
    echo '  <mirrors>' >> /root/.m2/settings.xml && \
    echo '    <mirror>' >> /root/.m2/settings.xml && \
    echo '      <id>aliyun</id>' >> /root/.m2/settings.xml && \
    echo '      <name>aliyun maven</name>' >> /root/.m2/settings.xml && \
    echo '      <url>https://maven.aliyun.com/repository/public</url>' >> /root/.m2/settings.xml && \
    echo '      <mirrorOf>central</mirrorOf>' >> /root/.m2/settings.xml && \
    echo '    </mirror>' >> /root/.m2/settings.xml && \
    echo '  </mirrors>' >> /root/.m2/settings.xml && \
    echo '</settings>' >> /root/.m2/settings.xml

# 首先复制pom文件，利用Docker缓存机制
COPY pom.xml .
COPY sdl-platform-admin/pom.xml ./sdl-platform-admin/
COPY sdl-platform-framework/pom.xml ./sdl-platform-framework/
COPY sdl-platform-system/pom.xml ./sdl-platform-system/
COPY sdl-platform-quartz/pom.xml ./sdl-platform-quartz/
COPY sdl-platform-generator/pom.xml ./sdl-platform-generator/
COPY sdl-platform-common/pom.xml ./sdl-platform-common/
COPY sdl-platform-business/pom.xml ./sdl-platform-business/

# 下载依赖（利用Docker层缓存）
RUN mvn dependency:go-offline -B

# 复制源代码
COPY sdl-platform-admin/src ./sdl-platform-admin/src
COPY sdl-platform-framework/src ./sdl-platform-framework/src
COPY sdl-platform-system/src ./sdl-platform-system/src
COPY sdl-platform-quartz/src ./sdl-platform-quartz/src
COPY sdl-platform-generator/src ./sdl-platform-generator/src
COPY sdl-platform-common/src ./sdl-platform-common/src
COPY sdl-platform-business/src ./sdl-platform-business/src

# 编译和打包
RUN mvn clean package -DskipTests -B

# 第二阶段：运行时镜像
FROM eclipse-temurin:17-jre

# 安装curl用于健康检查
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 从构建阶段复制jar文件
COPY --from=builder /app/sdl-platform-admin/target/sdl-platform-admin.jar app.jar

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-Dspring.profiles.active=public", "-jar", "app.jar"]
