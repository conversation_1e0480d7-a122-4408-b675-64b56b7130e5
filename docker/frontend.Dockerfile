# SDL Platform 前端多阶段构建镜像
# 第一阶段：构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm --registry=https://registry.npmmirror.com

# 设置pnpm镜像源
RUN pnpm config set registry https://registry.npmmirror.com

# 复制package文件
COPY sdl-platform-vue3/package.json ./
COPY sdl-platform-vue3/package-lock.json* ./
COPY sdl-platform-vue3/pnpm-lock.yaml* ./

# 清理pnpm缓存，确保干净的安装环境
RUN pnpm store prune

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY sdl-platform-vue3/ ./

# 构建生产版本
RUN pnpm run build:prod

# 第二阶段：运行时镜像
FROM nginx:alpine

# 安装curl用于健康检查
RUN apk add --no-cache curl

# 从构建阶段复制构建产物
COPY --from=builder /app/dist/ /usr/share/nginx/html/

# 复制nginx配置
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost/ || exit 1
