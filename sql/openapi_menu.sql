-- OpenAPI管理菜单 SQL
-- 系统管理菜单ID (假设为3)
SET @systemMenuId = 1;

-- 主菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('OpenAPI管理', @systemMenuId, '8', 'openapi', null, 1, 0, 'M', '0', '0', null, 'key', 'admin', sysdate(), '', null, 'OpenAPI管理目录');

-- 获取主菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 子菜单1: API密钥管理
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('API密钥管理', @parentId, '1', 'key', 'system/openapi/key/index', 1, 0, 'C', '0', '0', 'system:openapi:key:list', 'password', 'admin', sysdate(), '', null, 'API密钥管理菜单');

-- 获取API密钥管理菜单ID
SELECT @keyMenuId := LAST_INSERT_ID();

-- 子菜单2: 访问日志管理
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('访问日志', @parentId, '2', 'log', 'system/openapi/log/index', 1, 0, 'C', '0', '0', 'system:openapi:log:list', 'logininfor', 'admin', sysdate(), '', null, '访问日志菜单');

-- 获取访问日志菜单ID
SELECT @logMenuId := LAST_INSERT_ID();

-- API密钥管理按钮权限
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('API密钥查询', @keyMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'system:openapi:key:query', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('API密钥新增', @keyMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'system:openapi:key:add', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('API密钥修改', @keyMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'system:openapi:key:edit', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('API密钥删除', @keyMenuId, '4', '#', '', 1, 0, 'F', '0', '0', 'system:openapi:key:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('API密钥导出', @keyMenuId, '5', '#', '', 1, 0, 'F', '0', '0', 'system:openapi:key:export', '#', 'admin', sysdate(), '', null, '');

-- 访问日志管理按钮权限
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('访问日志查询', @logMenuId, '1', '#', '', 1, 0, 'F', '0', '0', 'system:openapi:log:query', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('访问日志删除', @logMenuId, '2', '#', '', 1, 0, 'F', '0', '0', 'system:openapi:log:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('访问日志导出', @logMenuId, '3', '#', '', 1, 0, 'F', '0', '0', 'system:openapi:log:export', '#', 'admin', sysdate(), '', null, '');

-- 为超级管理员角色分配权限 (角色ID通常为1)
INSERT INTO sys_role_menu (role_id, menu_id) 
SELECT 1, menu_id FROM sys_menu WHERE perms LIKE 'system:openapi:%' AND perms IS NOT NULL;