-- ----------------------------
-- OpenAPI相关表结构
-- ----------------------------

-- ----------------------------
-- 1、OpenAPI密钥管理表
-- ----------------------------
DROP TABLE IF EXISTS `openapi_key`;
CREATE TABLE `openapi_key` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `name` varchar(100) NOT NULL COMMENT '密钥名称',
  `status` char(1) NOT NULL DEFAULT '0' COMMENT '密钥状态（0正常 1停用）',
  `allowed_groups` varchar(500) DEFAULT NULL COMMENT '允许访问的权限组，多个用逗号分隔',
  `ip_whitelist` varchar(1000) DEFAULT NULL COMMENT 'IP白名单，多个IP用逗号分隔',
  `rate_limit` int DEFAULT 100 COMMENT '每小时请求限制次数',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `last_access_time` datetime DEFAULT NULL COMMENT '最后访问时间',
  `total_access_count` bigint DEFAULT 0 COMMENT '总访问次数',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_api_key` (`api_key`),
  KEY `idx_status` (`status`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OpenAPI密钥管理表';

-- ----------------------------
-- 2、OpenAPI访问日志表
-- ----------------------------
DROP TABLE IF EXISTS `openapi_access_log`;
CREATE TABLE `openapi_access_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `api_key` varchar(64) NOT NULL COMMENT 'API密钥',
  `request_uri` varchar(500) NOT NULL COMMENT '请求URI',
  `request_method` varchar(10) NOT NULL COMMENT '请求方法',
  `client_ip` varchar(45) NOT NULL COMMENT '客户端IP',
  `user_agent` text COMMENT '用户代理',
  `response_status` int NOT NULL COMMENT '响应状态码',
  `response_time` bigint NOT NULL COMMENT '响应时间（毫秒）',
  `request_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',
  `error_message` text COMMENT '错误信息',
  PRIMARY KEY (`id`),
  KEY `idx_api_key` (`api_key`),
  KEY `idx_request_time` (`request_time`),
  KEY `idx_response_status` (`response_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OpenAPI访问日志表';

-- ----------------------------
-- 初始化数据
-- ----------------------------
INSERT INTO `openapi_key` (`api_key`, `name`, `status`, `allowed_groups`, `ip_whitelist`, `rate_limit`, `expire_time`, `create_by`, `remark`) VALUES
('demo123456789012345678901234567890123456789012345678901234567890', '演示密钥', '0', 'default,user', '127.0.0.1,***********/24', 1000, DATE_ADD(NOW(), INTERVAL 1 YEAR), 'admin', '系统演示用API密钥'),
('TWT5FbkZPNTr0fspQf7GhaspR3cyCg3ktJ0W5SAErHsgwvJweyrrxXvL4xtF21I4', '用户测试密钥', '0', 'default,user,product,order', NULL, 1000, DATE_ADD(NOW(), INTERVAL 1 YEAR), 'admin', '用户测试API密钥，允许访问所有权限组');