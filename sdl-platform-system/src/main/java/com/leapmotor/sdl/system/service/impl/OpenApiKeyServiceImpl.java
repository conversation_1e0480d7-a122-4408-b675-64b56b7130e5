package com.leapmotor.sdl.system.service.impl;

import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.leapmotor.sdl.common.core.redis.RedisCache;
import com.leapmotor.sdl.common.utils.DateUtils;
import com.leapmotor.sdl.common.utils.SecurityUtils;
import com.leapmotor.sdl.common.utils.StringUtils;
import com.leapmotor.sdl.common.utils.openapi.OpenApiUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.leapmotor.sdl.system.mapper.OpenApiKeyMapper;
import com.leapmotor.sdl.common.core.domain.OpenApiKey;
import com.leapmotor.sdl.system.service.IOpenApiKeyService;

/**
 * OpenAPI密钥管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class OpenApiKeyServiceImpl implements IOpenApiKeyService 
{
    @Autowired
    private OpenApiKeyMapper openApiKeyMapper;

    @Autowired
    private RedisCache redisCache;

    /**
     * 查询OpenAPI密钥
     * 
     * @param id OpenAPI密钥主键
     * @return OpenAPI密钥
     */
    @Override
    public OpenApiKey selectOpenApiKeyById(Long id)
    {
        return openApiKeyMapper.selectOpenApiKeyById(id);
    }

    /**
     * 根据API密钥查询OpenAPI密钥信息
     * 
     * 先从Redis缓存中查找，如果没有则从数据库查询并缓存
     * 
     * @param apiKey API密钥
     * @return OpenAPI密钥
     */
    @Override
    public OpenApiKey selectOpenApiKeyByApiKey(String apiKey)
    {
        if (StringUtils.isEmpty(apiKey))
        {
            return null;
        }

        // 先从Redis缓存中获取
        String cacheKey = OpenApiUtils.getCacheKey(apiKey);
        OpenApiKey openApiKey = redisCache.getCacheObject(cacheKey);

        if (openApiKey == null)
        {
            // 缓存中没有，从数据库查询
            openApiKey = openApiKeyMapper.selectOpenApiKeyByApiKey(apiKey);
            if (openApiKey != null)
            {
                // 缓存到Redis，有效期1小时
                redisCache.setCacheObject(cacheKey, openApiKey, 1, TimeUnit.HOURS);
            }
        }

        return openApiKey;
    }

    /**
     * 查询OpenAPI密钥列表
     * 
     * @param openApiKey OpenAPI密钥
     * @return OpenAPI密钥
     */
    @Override
    public List<OpenApiKey> selectOpenApiKeyList(OpenApiKey openApiKey)
    {
        return openApiKeyMapper.selectOpenApiKeyList(openApiKey);
    }

    /**
     * 新增OpenAPI密钥
     * 
     * 会自动生成API密钥，并将信息缓存到Redis
     * 
     * @param openApiKey OpenAPI密钥
     * @return 创建的OpenAPI密钥对象
     */
    @Override
    public OpenApiKey insertOpenApiKey(OpenApiKey openApiKey)
    {
        // 生成API密钥
        String apiKey = OpenApiUtils.generateApiKey();
        openApiKey.setApiKey(apiKey);
        
        // 设置默认值
        if (StringUtils.isEmpty(openApiKey.getStatus()))
        {
            openApiKey.setStatus("0"); // 默认正常状态
        }
        if (openApiKey.getRateLimit() == null)
        {
            openApiKey.setRateLimit(100); // 默认每小时100次
        }
        if (openApiKey.getTotalAccessCount() == null)
        {
            openApiKey.setTotalAccessCount(0L);
        }
        
        openApiKey.setCreateBy(SecurityUtils.getUsername());
        openApiKey.setCreateTime(DateUtils.getNowDate());
        
        int result = openApiKeyMapper.insertOpenApiKey(openApiKey);
        
        if (result > 0)
        {
            // 缓存到Redis
            String cacheKey = OpenApiUtils.getCacheKey(apiKey);
            redisCache.setCacheObject(cacheKey, openApiKey, 1, TimeUnit.HOURS);
            return openApiKey;
        }
        
        return null;
    }

    /**
     * 修改OpenAPI密钥
     * 
     * 修改后会更新Redis缓存
     * 
     * @param openApiKey OpenAPI密钥
     * @return 结果
     */
    @Override
    public int updateOpenApiKey(OpenApiKey openApiKey)
    {
        openApiKey.setUpdateBy(SecurityUtils.getUsername());
        openApiKey.setUpdateTime(DateUtils.getNowDate());
        
        int result = openApiKeyMapper.updateOpenApiKey(openApiKey);
        
        if (result > 0)
        {
            // 更新Redis缓存
            String cacheKey = OpenApiUtils.getCacheKey(openApiKey.getApiKey());
            redisCache.setCacheObject(cacheKey, openApiKey, 1, TimeUnit.HOURS);
        }
        
        return result;
    }

    /**
     * 批量删除OpenAPI密钥
     * 
     * 删除时会同时清理Redis缓存
     * 
     * @param ids 需要删除的OpenAPI密钥主键集合
     * @return 结果
     */
    @Override
    public int deleteOpenApiKeyByIds(Long[] ids)
    {
        // 先查询要删除的API密钥信息，用于清理缓存
        for (Long id : ids)
        {
            OpenApiKey openApiKey = openApiKeyMapper.selectOpenApiKeyById(id);
            if (openApiKey != null)
            {
                // 清理Redis缓存
                String cacheKey = OpenApiUtils.getCacheKey(openApiKey.getApiKey());
                redisCache.deleteObject(cacheKey);
                
                // 清理限流缓存
                String rateLimitKey = OpenApiUtils.getRateLimitKey(openApiKey.getApiKey());
                redisCache.deleteObject(rateLimitKey);
            }
        }
        
        return openApiKeyMapper.deleteOpenApiKeyByIds(ids);
    }

    /**
     * 删除OpenAPI密钥信息
     * 
     * @param id OpenAPI密钥主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiKeyById(Long id)
    {
        // 先查询API密钥信息
        OpenApiKey openApiKey = openApiKeyMapper.selectOpenApiKeyById(id);
        if (openApiKey != null)
        {
            // 清理Redis缓存
            String cacheKey = OpenApiUtils.getCacheKey(openApiKey.getApiKey());
            redisCache.deleteObject(cacheKey);
            
            // 清理限流缓存
            String rateLimitKey = OpenApiUtils.getRateLimitKey(openApiKey.getApiKey());
            redisCache.deleteObject(rateLimitKey);
        }
        
        return openApiKeyMapper.deleteOpenApiKeyById(id);
    }

    /**
     * 重新生成API密钥
     * 
     * @param id OpenAPI密钥主键
     * @return 新的API密钥
     */
    @Override
    public String regenerateApiKey(Long id)
    {
        OpenApiKey openApiKey = openApiKeyMapper.selectOpenApiKeyById(id);
        if (openApiKey == null)
        {
            return null;
        }
        
        // 清理旧的缓存
        String oldCacheKey = OpenApiUtils.getCacheKey(openApiKey.getApiKey());
        redisCache.deleteObject(oldCacheKey);
        
        String oldRateLimitKey = OpenApiUtils.getRateLimitKey(openApiKey.getApiKey());
        redisCache.deleteObject(oldRateLimitKey);
        
        // 生成新的API密钥
        String newApiKey = OpenApiUtils.generateApiKey();
        openApiKey.setApiKey(newApiKey);
        openApiKey.setUpdateBy(SecurityUtils.getUsername());
        openApiKey.setUpdateTime(DateUtils.getNowDate());
        
        int result = openApiKeyMapper.updateOpenApiKey(openApiKey);
        
        if (result > 0)
        {
            // 缓存新的API密钥信息
            String newCacheKey = OpenApiUtils.getCacheKey(newApiKey);
            redisCache.setCacheObject(newCacheKey, openApiKey, 1, TimeUnit.HOURS);
            
            return newApiKey;
        }
        
        return null;
    }

    /**
     * 更新API密钥状态
     * 
     * @param id OpenAPI密钥主键
     * @param status 状态（0正常 1停用）
     * @return 结果
     */
    @Override
    public int updateApiKeyStatus(Long id, String status)
    {
        OpenApiKey openApiKey = new OpenApiKey();
        openApiKey.setId(id);
        openApiKey.setStatus(status);
        openApiKey.setUpdateBy(SecurityUtils.getUsername());
        openApiKey.setUpdateTime(DateUtils.getNowDate());
        
        int result = openApiKeyMapper.updateOpenApiKey(openApiKey);
        
        if (result > 0)
        {
            // 更新缓存或清理缓存
            OpenApiKey fullInfo = openApiKeyMapper.selectOpenApiKeyById(id);
            if (fullInfo != null)
            {
                String cacheKey = OpenApiUtils.getCacheKey(fullInfo.getApiKey());
                if ("1".equals(status))
                {
                    // 停用时清理缓存
                    redisCache.deleteObject(cacheKey);
                }
                else
                {
                    // 启用时更新缓存
                    redisCache.setCacheObject(cacheKey, fullInfo, 1, TimeUnit.HOURS);
                }
            }
        }
        
        return result;
    }

    /**
     * 更新API密钥使用信息
     * 
     * 异步更新最后访问时间和总访问次数
     * 
     * @param apiKey API密钥
     * @return 结果
     */
    @Override
    public int updateApiKeyUsage(String apiKey)
    {
        return openApiKeyMapper.updateApiKeyUsage(apiKey);
    }

    /**
     * 检查API密钥是否唯一
     * 
     * @param openApiKey OpenAPI密钥信息
     * @return 结果
     */
    @Override
    public boolean checkApiKeyUnique(OpenApiKey openApiKey)
    {
        Long id = StringUtils.isNull(openApiKey.getId()) ? -1L : openApiKey.getId();
        OpenApiKey info = openApiKeyMapper.selectOpenApiKeyByApiKey(openApiKey.getApiKey());
        if (StringUtils.isNotNull(info) && info.getId().longValue() != id.longValue())
        {
            return false;
        }
        return true;
    }
}