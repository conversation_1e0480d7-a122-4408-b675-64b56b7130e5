package com.leapmotor.sdl.system.service;

import java.util.List;
import com.leapmotor.sdl.common.core.domain.OpenApiKey;

/**
 * OpenAPI密钥管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface IOpenApiKeyService 
{
    /**
     * 查询OpenAPI密钥
     * 
     * @param id OpenAPI密钥主键
     * @return OpenAPI密钥
     */
    public OpenApiKey selectOpenApiKeyById(Long id);

    /**
     * 根据API密钥查询OpenAPI密钥信息
     * 
     * 此方法会先从Redis缓存中查找，如果没有则从数据库查询并缓存
     * 
     * @param apiKey API密钥
     * @return OpenAPI密钥
     */
    public OpenApiKey selectOpenApiKeyByApiKey(String apiKey);

    /**
     * 查询OpenAPI密钥列表
     * 
     * @param openApiKey OpenAPI密钥
     * @return OpenAPI密钥集合
     */
    public List<OpenApiKey> selectOpenApiKeyList(OpenApiKey openApiKey);

    /**
     * 新增OpenAPI密钥
     * 
     * 会自动生成API密钥，并将信息缓存到Redis
     * 
     * @param openApiKey OpenAPI密钥
     * @return 创建的OpenAPI密钥对象
     */
    public OpenApiKey insertOpenApiKey(OpenApiKey openApiKey);

    /**
     * 修改OpenAPI密钥
     * 
     * 修改后会更新Redis缓存
     * 
     * @param openApiKey OpenAPI密钥
     * @return 结果
     */
    public int updateOpenApiKey(OpenApiKey openApiKey);

    /**
     * 批量删除OpenAPI密钥
     * 
     * 删除时会同时清理Redis缓存
     * 
     * @param ids 需要删除的OpenAPI密钥主键集合
     * @return 结果
     */
    public int deleteOpenApiKeyByIds(Long[] ids);

    /**
     * 删除OpenAPI密钥信息
     * 
     * @param id OpenAPI密钥主键
     * @return 结果
     */
    public int deleteOpenApiKeyById(Long id);

    /**
     * 重新生成API密钥
     * 
     * @param id OpenAPI密钥主键
     * @return 新的API密钥
     */
    public String regenerateApiKey(Long id);

    /**
     * 更新API密钥状态
     * 
     * @param id OpenAPI密钥主键
     * @param status 状态（0正常 1停用）
     * @return 结果
     */
    public int updateApiKeyStatus(Long id, String status);

    /**
     * 更新API密钥使用信息
     * 
     * 异步更新最后访问时间和总访问次数
     * 
     * @param apiKey API密钥
     * @return 结果
     */
    public int updateApiKeyUsage(String apiKey);

    /**
     * 检查API密钥是否唯一
     * 
     * @param openApiKey OpenAPI密钥信息
     * @return 结果
     */
    public boolean checkApiKeyUnique(OpenApiKey openApiKey);
}