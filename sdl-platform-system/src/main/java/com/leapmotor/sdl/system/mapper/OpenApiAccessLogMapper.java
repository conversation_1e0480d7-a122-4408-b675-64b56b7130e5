package com.leapmotor.sdl.system.mapper;

import com.leapmotor.sdl.common.core.domain.OpenApiAccessLog;
import java.util.List;

/**
 * OpenAPI访问日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface OpenApiAccessLogMapper 
{
    /**
     * 查询OpenAPI访问日志
     * 
     * @param id OpenAPI访问日志主键
     * @return OpenAPI访问日志
     */
    public OpenApiAccessLog selectOpenApiAccessLogById(Long id);

    /**
     * 查询OpenAPI访问日志列表
     * 
     * @param openApiAccessLog OpenAPI访问日志
     * @return OpenAPI访问日志集合
     */
    public List<OpenApiAccessLog> selectOpenApiAccessLogList(OpenApiAccessLog openApiAccessLog);

    /**
     * 新增OpenAPI访问日志
     *
     * @param openApiAccessLog OpenAPI访问日志
     * @return 结果
     */
    public int insertOpenApiAccessLog(OpenApiAccessLog openApiAccessLog);

    /**
     * 修改OpenAPI访问日志
     *
     * @param openApiAccessLog OpenAPI访问日志
     * @return 结果
     */
    public int updateOpenApiAccessLog(OpenApiAccessLog openApiAccessLog);

    /**
     * 删除OpenAPI访问日志
     *
     * @param id OpenAPI访问日志主键
     * @return 结果
     */
    public int deleteOpenApiAccessLogById(Long id);

    /**
     * 批量删除OpenAPI访问日志
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiAccessLogByIds(Long[] ids);

    /**
     * 根据API密钥查询访问统计
     * 
     * @param apiKey API密钥
     * @return 访问统计信息
     */
    public List<OpenApiAccessLog> selectAccessStatsByApiKey(String apiKey);

    /**
     * 清理过期的访问日志
     * 
     * @param days 保留天数
     * @return 删除的记录数
     */
    public int cleanExpiredAccessLogs(int days);
}