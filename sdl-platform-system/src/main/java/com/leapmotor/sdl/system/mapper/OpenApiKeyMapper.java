package com.leapmotor.sdl.system.mapper;

import com.leapmotor.sdl.common.core.domain.OpenApiKey;
import java.util.List;

/**
 * OpenAPI密钥管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface OpenApiKeyMapper 
{
    /**
     * 查询OpenAPI密钥
     * 
     * @param id OpenAPI密钥主键
     * @return OpenAPI密钥
     */
    public OpenApiKey selectOpenApiKeyById(Long id);

    /**
     * 根据API密钥查询OpenAPI密钥信息
     * 
     * @param apiKey API密钥
     * @return OpenAPI密钥
     */
    public OpenApiKey selectOpenApiKeyByApiKey(String apiKey);

    /**
     * 查询OpenAPI密钥列表
     * 
     * @param openApiKey OpenAPI密钥
     * @return OpenAPI密钥集合
     */
    public List<OpenApiKey> selectOpenApiKeyList(OpenApiKey openApiKey);

    /**
     * 新增OpenAPI密钥
     * 
     * @param openApiKey OpenAPI密钥
     * @return 结果
     */
    public int insertOpenApiKey(OpenApiKey openApiKey);

    /**
     * 修改OpenAPI密钥
     * 
     * @param openApiKey OpenAPI密钥
     * @return 结果
     */
    public int updateOpenApiKey(OpenApiKey openApiKey);

    /**
     * 删除OpenAPI密钥
     * 
     * @param id OpenAPI密钥主键
     * @return 结果
     */
    public int deleteOpenApiKeyById(Long id);

    /**
     * 批量删除OpenAPI密钥
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOpenApiKeyByIds(Long[] ids);

    /**
     * 更新API密钥使用信息
     * 
     * @param apiKey API密钥
     * @return 结果
     */
    public int updateApiKeyUsage(String apiKey);
}