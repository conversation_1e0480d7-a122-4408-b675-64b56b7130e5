package com.leapmotor.sdl.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import com.leapmotor.sdl.system.mapper.OpenApiAccessLogMapper;
import com.leapmotor.sdl.common.core.domain.OpenApiAccessLog;
import com.leapmotor.sdl.system.service.IOpenApiAccessLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * OpenAPI访问日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Service
public class OpenApiAccessLogServiceImpl implements IOpenApiAccessLogService 
{
    private static final Logger log = LoggerFactory.getLogger(OpenApiAccessLogServiceImpl.class);

    @Autowired
    private OpenApiAccessLogMapper openApiAccessLogMapper;

    /**
     * 查询OpenAPI访问日志
     * 
     * @param id OpenAPI访问日志主键
     * @return OpenAPI访问日志
     */
    @Override
    public OpenApiAccessLog selectOpenApiAccessLogById(Long id)
    {
        return openApiAccessLogMapper.selectOpenApiAccessLogById(id);
    }

    /**
     * 查询OpenAPI访问日志列表
     * 
     * @param openApiAccessLog OpenAPI访问日志
     * @return OpenAPI访问日志
     */
    @Override
    public List<OpenApiAccessLog> selectOpenApiAccessLogList(OpenApiAccessLog openApiAccessLog)
    {
        return openApiAccessLogMapper.selectOpenApiAccessLogList(openApiAccessLog);
    }

    /**
     * 新增OpenAPI访问日志
     *
     * @param openApiAccessLog OpenAPI访问日志
     * @return 结果
     */
    @Override
    public int insertOpenApiAccessLog(OpenApiAccessLog openApiAccessLog)
    {
        return openApiAccessLogMapper.insertOpenApiAccessLog(openApiAccessLog);
    }

    /**
     * 修改OpenAPI访问日志
     *
     * @param openApiAccessLog OpenAPI访问日志
     * @return 结果
     */
    @Override
    public int updateOpenApiAccessLog(OpenApiAccessLog openApiAccessLog)
    {
        return openApiAccessLogMapper.updateOpenApiAccessLog(openApiAccessLog);
    }

    /**
     * 批量删除OpenAPI访问日志
     *
     * @param ids 需要删除的OpenAPI访问日志主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiAccessLogByIds(Long[] ids)
    {
        return openApiAccessLogMapper.deleteOpenApiAccessLogByIds(ids);
    }

    /**
     * 删除OpenAPI访问日志信息
     *
     * @param id OpenAPI访问日志主键
     * @return 结果
     */
    @Override
    public int deleteOpenApiAccessLogById(Long id)
    {
        return openApiAccessLogMapper.deleteOpenApiAccessLogById(id);
    }

    /**
     * 根据API密钥查询访问统计
     * 
     * @param apiKey API密钥
     * @return 访问统计信息
     */
    @Override
    public List<OpenApiAccessLog> selectAccessStatsByApiKey(String apiKey)
    {
        return openApiAccessLogMapper.selectAccessStatsByApiKey(apiKey);
    }

    /**
     * 清理过期的访问日志
     * 
     * 定时任务调用，清理指定天数之前的访问日志
     * 
     * @param days 保留天数
     * @return 删除的记录数
     */
    @Override
    public int cleanExpiredAccessLogs(int days)
    {
        try 
        {
            int deletedCount = openApiAccessLogMapper.cleanExpiredAccessLogs(days);
            log.info("清理过期访问日志完成，共删除{}条记录", deletedCount);
            return deletedCount;
        } 
        catch (Exception e) 
        {
            log.error("清理过期访问日志时发生错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 异步记录访问日志
     * 
     * 使用异步方式记录，提高接口性能
     * 
     * @param openApiAccessLog OpenAPI访问日志
     */
    @Override
    @Async
    public void recordAccessLogAsync(OpenApiAccessLog openApiAccessLog)
    {
        try 
        {
            openApiAccessLogMapper.insertOpenApiAccessLog(openApiAccessLog);
        } 
        catch (Exception e) 
        {
            log.error("异步记录OpenAPI访问日志时发生错误: {}", e.getMessage(), e);
        }
    }
}