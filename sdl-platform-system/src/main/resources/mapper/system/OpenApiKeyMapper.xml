<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.leapmotor.sdl.system.mapper.OpenApiKeyMapper">
    
    <resultMap type="OpenApiKey" id="OpenApiKeyResult">
        <result property="id"    column="id"    />
        <result property="apiKey"    column="api_key"    />
        <result property="name"    column="name"    />
        <result property="status"    column="status"    />
        <result property="allowedGroups"    column="allowed_groups"    />
        <result property="ipWhitelist"    column="ip_whitelist"    />
        <result property="rateLimit"    column="rate_limit"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="lastAccessTime"    column="last_access_time"    />
        <result property="totalAccessCount"    column="total_access_count"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectOpenApiKeyVo">
        select id, api_key, name, status, allowed_groups, ip_whitelist, rate_limit, expire_time, last_access_time, total_access_count, create_by, create_time, update_by, update_time, remark from openapi_key
    </sql>

    <select id="selectOpenApiKeyList" parameterType="OpenApiKey" resultMap="OpenApiKeyResult">
        <include refid="selectOpenApiKeyVo"/>
        <where>  
            <if test="apiKey != null  and apiKey != ''"> and api_key = #{apiKey}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="allowedGroups != null  and allowedGroups != ''"> and allowed_groups like concat('%', #{allowedGroups}, '%')</if>
            <if test="ipWhitelist != null  and ipWhitelist != ''"> and ip_whitelist like concat('%', #{ipWhitelist}, '%')</if>
            <if test="rateLimit != null "> and rate_limit = #{rateLimit}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="lastAccessTime != null "> and last_access_time = #{lastAccessTime}</if>
            <if test="totalAccessCount != null "> and total_access_count = #{totalAccessCount}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectOpenApiKeyById" parameterType="Long" resultMap="OpenApiKeyResult">
        <include refid="selectOpenApiKeyVo"/>
        where id = #{id}
    </select>

    <select id="selectOpenApiKeyByApiKey" parameterType="String" resultMap="OpenApiKeyResult">
        <include refid="selectOpenApiKeyVo"/>
        where api_key = #{apiKey}
    </select>
        
    <insert id="insertOpenApiKey" parameterType="OpenApiKey" useGeneratedKeys="true" keyProperty="id">
        insert into openapi_key
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="apiKey != null and apiKey != ''">api_key,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="status != null">status,</if>
            <if test="allowedGroups != null">allowed_groups,</if>
            <if test="ipWhitelist != null">ip_whitelist,</if>
            <if test="rateLimit != null">rate_limit,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="lastAccessTime != null">last_access_time,</if>
            <if test="totalAccessCount != null">total_access_count,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="apiKey != null and apiKey != ''">#{apiKey},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="status != null">#{status},</if>
            <if test="allowedGroups != null">#{allowedGroups},</if>
            <if test="ipWhitelist != null">#{ipWhitelist},</if>
            <if test="rateLimit != null">#{rateLimit},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="lastAccessTime != null">#{lastAccessTime},</if>
            <if test="totalAccessCount != null">#{totalAccessCount},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateOpenApiKey" parameterType="OpenApiKey">
        update openapi_key
        <trim prefix="SET" suffixOverrides=",">
            <if test="apiKey != null and apiKey != ''">api_key = #{apiKey},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="status != null">status = #{status},</if>
            <if test="allowedGroups != null">allowed_groups = #{allowedGroups},</if>
            <if test="ipWhitelist != null">ip_whitelist = #{ipWhitelist},</if>
            <if test="rateLimit != null">rate_limit = #{rateLimit},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="lastAccessTime != null">last_access_time = #{lastAccessTime},</if>
            <if test="totalAccessCount != null">total_access_count = #{totalAccessCount},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiKeyById" parameterType="Long">
        delete from openapi_key where id = #{id}
    </delete>

    <delete id="deleteOpenApiKeyByIds" parameterType="String">
        delete from openapi_key where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateApiKeyUsage" parameterType="String">
        update openapi_key set 
            last_access_time = now(),
            total_access_count = total_access_count + 1
        where api_key = #{apiKey}
    </update>

</mapper>