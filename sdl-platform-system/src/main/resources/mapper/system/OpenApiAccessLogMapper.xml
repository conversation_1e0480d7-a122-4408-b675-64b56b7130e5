<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.leapmotor.sdl.system.mapper.OpenApiAccessLogMapper">
    
    <resultMap type="OpenApiAccessLog" id="OpenApiAccessLogResult">
        <result property="id"    column="id"    />
        <result property="apiKey"    column="api_key"    />
        <result property="requestUri"    column="request_uri"    />
        <result property="requestMethod"    column="request_method"    />
        <result property="clientIp"    column="client_ip"    />
        <result property="userAgent"    column="user_agent"    />
        <result property="responseStatus"    column="response_status"    />
        <result property="responseTime"    column="response_time"    />
        <result property="requestTime"    column="request_time"    />
        <result property="errorMessage"    column="error_message"    />
    </resultMap>

    <sql id="selectOpenApiAccessLogVo">
        select id, api_key, request_uri, request_method, client_ip, user_agent, response_status, response_time, request_time, error_message from openapi_access_log
    </sql>

    <select id="selectOpenApiAccessLogList" parameterType="OpenApiAccessLog" resultMap="OpenApiAccessLogResult">
        <include refid="selectOpenApiAccessLogVo"/>
        <where>  
            <if test="apiKey != null  and apiKey != ''"> and api_key = #{apiKey}</if>
            <if test="requestUri != null  and requestUri != ''"> and request_uri like concat('%', #{requestUri}, '%')</if>
            <if test="requestMethod != null  and requestMethod != ''"> and request_method = #{requestMethod}</if>
            <if test="clientIp != null  and clientIp != ''"> and client_ip like concat('%', #{clientIp}, '%')</if>
            <if test="userAgent != null  and userAgent != ''"> and user_agent like concat('%', #{userAgent}, '%')</if>
            <if test="responseStatus != null "> and response_status = #{responseStatus}</if>
            <if test="responseTime != null "> and response_time = #{responseTime}</if>
            <if test="requestTime != null "> and request_time = #{requestTime}</if>
            <if test="errorMessage != null  and errorMessage != ''"> and error_message like concat('%', #{errorMessage}, '%')</if>
        </where>
        order by request_time desc
    </select>
    
    <select id="selectOpenApiAccessLogById" parameterType="Long" resultMap="OpenApiAccessLogResult">
        <include refid="selectOpenApiAccessLogVo"/>
        where id = #{id}
    </select>

    <select id="selectAccessStatsByApiKey" parameterType="String" resultMap="OpenApiAccessLogResult">
        select 
            api_key,
            count(*) as total_count,
            count(case when response_status = 200 then 1 end) as success_count,
            count(case when response_status != 200 then 1 end) as error_count,
            avg(response_time) as avg_response_time,
            max(request_time) as last_request_time
        from openapi_access_log
        where api_key = #{apiKey}
        group by api_key
    </select>

    <insert id="insertOpenApiAccessLog" parameterType="OpenApiAccessLog" useGeneratedKeys="true" keyProperty="id">
        insert into openapi_access_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="apiKey != null and apiKey != ''">api_key,</if>
            <if test="requestUri != null and requestUri != ''">request_uri,</if>
            <if test="requestMethod != null and requestMethod != ''">request_method,</if>
            <if test="clientIp != null and clientIp != ''">client_ip,</if>
            <if test="userAgent != null">user_agent,</if>
            <if test="responseStatus != null">response_status,</if>
            <if test="responseTime != null">response_time,</if>
            <if test="requestTime != null">request_time,</if>
            <if test="errorMessage != null">error_message,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="apiKey != null and apiKey != ''">#{apiKey},</if>
            <if test="requestUri != null and requestUri != ''">#{requestUri},</if>
            <if test="requestMethod != null and requestMethod != ''">#{requestMethod},</if>
            <if test="clientIp != null and clientIp != ''">#{clientIp},</if>
            <if test="userAgent != null">#{userAgent},</if>
            <if test="responseStatus != null">#{responseStatus},</if>
            <if test="responseTime != null">#{responseTime},</if>
            <if test="requestTime != null">#{requestTime},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
         </trim>
    </insert>

    <update id="updateOpenApiAccessLog" parameterType="OpenApiAccessLog">
        update openapi_access_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="apiKey != null and apiKey != ''">api_key = #{apiKey},</if>
            <if test="requestUri != null and requestUri != ''">request_uri = #{requestUri},</if>
            <if test="requestMethod != null and requestMethod != ''">request_method = #{requestMethod},</if>
            <if test="clientIp != null and clientIp != ''">client_ip = #{clientIp},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="responseStatus != null">response_status = #{responseStatus},</if>
            <if test="responseTime != null">response_time = #{responseTime},</if>
            <if test="requestTime != null">request_time = #{requestTime},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOpenApiAccessLogById" parameterType="Long">
        delete from openapi_access_log where id = #{id}
    </delete>

    <delete id="deleteOpenApiAccessLogByIds" parameterType="String">
        delete from openapi_access_log where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="cleanExpiredAccessLogs" parameterType="int">
        delete from openapi_access_log 
        where request_time &lt; date_sub(now(), interval #{days} day)
    </delete>

</mapper>