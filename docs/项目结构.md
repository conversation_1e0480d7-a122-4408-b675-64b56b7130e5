# SDL Platform 项目结构

## 根目录文件

### 配置文件
- `docker-compose.yml` - Docker编排配置
- `pom.xml` - Maven主配置文件
- `.dockerignore` - Docker构建忽略文件
- `.gitignore` - Git忽略文件

## 目录结构

```
sdl-platform/
├── bin/                           # 🔧 可执行脚本目录
│   ├── deploy-docker.sh           # Docker一键部署脚本
│   ├── quick-update-docker.sh     # 快速更新脚本
│   └── build.sh                   # 传统构建脚本
│
├── docs/                          # 📚 文档目录
│   ├── 快速开始.md                 # 快速开始指南
│   ├── 项目结构.md                 # 本文档
│   └── 常见问题.md                 # 问题排查指南
│
├── docker/                        # 🐳 Docker相关文件
│   ├── backend.Dockerfile         # 后端镜像构建文件
│   ├── frontend.Dockerfile        # 前端镜像构建文件
│   └── nginx.conf                 # Nginx配置文件
│
├── sdl-platform-admin/            # 🎯 主应用模块
│   ├── src/                       # 源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-framework/        # 🏗️ 框架核心模块
│   ├── src/                       # 框架源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-system/           # 👥 系统管理模块
│   ├── src/                       # 系统管理源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-common/           # 🔧 通用工具模块
│   ├── src/                       # 通用工具源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-business/         # 💼 业务模块
│   ├── src/                       # 业务源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-quartz/           # ⏰ 定时任务模块
│   ├── src/                       # 定时任务源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-generator/        # 🔄 代码生成模块
│   ├── src/                       # 代码生成源代码
│   └── pom.xml                    # Maven配置
│
├── sdl-platform-vue3/             # 🎨 前端Vue3项目
│   ├── src/                       # Vue3源代码
│   ├── public/                    # 静态资源
│   ├── package.json               # npm配置
│   ├── vite.config.js             # Vite配置
│   └── dist/                      # 构建产物（忽略）
│
├── sql/                           # 🗄️ 数据库脚本
│   ├── ry_20250522.sql            # 主数据库脚本
│   ├── quartz.sql                 # 定时任务表
│   ├── openapi_tables.sql         # OpenAPI表结构
│   └── openapi_menu.sql           # OpenAPI菜单数据
│

```

## 模块说明

### 后端模块

| 模块 | 说明 | 主要功能 |
|------|------|----------|
| `sdl-platform-admin` | 主应用模块 | 启动类、主要配置 |
| `sdl-platform-framework` | 框架核心 | 安全、缓存、配置等 |
| `sdl-platform-system` | 系统管理 | 用户、角色、权限管理 |
| `sdl-platform-common` | 通用工具 | 工具类、常量、异常等 |
| `sdl-platform-business` | 业务模块 | 具体业务逻辑 |
| `sdl-platform-quartz` | 定时任务 | 任务调度管理 |
| `sdl-platform-generator` | 代码生成 | 自动生成CRUD代码 |

### 前端模块

| 目录 | 说明 |
|------|------|
| `src/` | Vue3源代码 |
| `src/views/` | 页面组件 |
| `src/components/` | 通用组件 |
| `src/api/` | API接口定义 |
| `src/utils/` | 工具函数 |
| `src/router/` | 路由配置 |
| `src/store/` | 状态管理 |

## 构建产物

### Docker构建
- 后端：`sdl-platform-admin/target/sdl-platform-admin.jar`
- 前端：`sdl-platform-vue3/dist/`

### 数据持久化
- `backend_uploads` - 文件上传存储
- `backend_logs` - 应用日志存储

## 开发建议

1. **后端开发**：修改Java代码后运行 `./bin/quick-update-docker.sh backend`
2. **前端开发**：修改Vue代码后运行 `./bin/quick-update-docker.sh frontend`
3. **全量更新**：运行 `./bin/quick-update-docker.sh` 更新所有服务
4. **查看日志**：运行 `./bin/quick-update-docker.sh logs` 查看实时日志

## 脚本说明

### bin/deploy-docker.sh
Docker一键部署脚本，功能包括：
- 检查Docker环境
- 拉取基础镜像
- 清理旧容器和镜像
- 构建新镜像
- 启动服务
- 健康检查

### bin/quick-update-docker.sh
快速更新脚本，用于开发过程中的代码更新：
- 支持更新所有服务或指定服务
- 支持查看服务状态和日志
- 自动重启相关容器

### bin/build.sh
传统构建脚本，需要本地安装开发环境：
- 检查Java、Maven、Node.js环境
- 编译后端Java代码
- 构建前端Vue3应用
- 生成部署文件
