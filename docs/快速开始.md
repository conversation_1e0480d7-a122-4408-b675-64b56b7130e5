# SDL Platform 快速开始

## 部署方式选择

### Docker 部署（推荐）
适用于任意主机，无需安装开发环境：
```bash
./bin/deploy-docker.sh
```

### 传统部署
需要宿主机安装Java、Maven、Node.js：
```bash
./bin/build.sh
```

## Docker 部署详细步骤

### 1. 环境检查
确保已安装Docker：
```bash
docker --version
docker compose version
```

### 2. 一键部署
```bash
# 完整部署
./bin/deploy-docker.sh

# 跳过镜像拉取（重复部署时）
./bin/deploy-docker.sh --skip-pull

# 跳过清理步骤（保留现有数据）
./bin/deploy-docker.sh --skip-cleanup
```

### 3. 验证部署
- 前端：http://localhost
- 后端：http://localhost:8080
- 健康检查：http://localhost:8080/actuator/health

## 开发工作流

### 代码修改后更新
```bash
# 快速更新所有服务
./bin/quick-update-docker.sh

# 仅更新特定服务
./bin/quick-update-docker.sh backend   # 仅后端
./bin/quick-update-docker.sh frontend  # 仅前端
```

### 查看运行状态
```bash
# 服务状态
./bin/quick-update-docker.sh status

# 实时日志
./bin/quick-update-docker.sh logs
```

## 常用Docker命令

### 容器管理
```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 进入容器
docker exec -it sdl-backend bash
docker exec -it sdl-frontend sh
```

### 数据管理
```bash
# 查看数据卷
docker volume ls

# 备份数据
docker run --rm -v backend_uploads:/data -v $(pwd):/backup alpine tar czf /backup/uploads.tar.gz -C /data .

# 恢复数据
docker run --rm -v backend_uploads:/data -v $(pwd):/backup alpine tar xzf /backup/uploads.tar.gz -C /data
```

## 技术架构

### 多阶段构建
- **后端**: Maven容器编译 → JRE容器运行
- **前端**: Node.js容器构建 → Nginx容器运行

### 使用的镜像
- `maven:3.9-eclipse-temurin-17` - 后端编译
- `node:18-alpine` - 前端编译
- `eclipse-temurin:17-jre` - 后端运行
- `nginx:alpine` - 前端运行

### 数据持久化
- `backend_uploads` - 上传文件存储
- `backend_logs` - 应用日志

## 维护

### 定期清理
```bash
# 清理未使用的镜像
docker image prune -f

# 清理构建缓存
docker builder prune -f

# 清理所有未使用资源
docker system prune -f
```

### 版本升级
1. 更新代码
2. 运行 `./bin/quick-update-docker.sh`
3. 验证服务正常

## 下一步

- 项目架构：[项目结构](项目结构.md)
- 问题排查：[常见问题](常见问题.md)
