# OpenAPI功能使用说明

## 概述

SDL Platform 已集成OpenAPI功能，支持通过API Key进行接口访问认证，具备权限组管理、IP白名单、访问频率限制等安全特性。

## 功能特性

### 1. API Key管理
- 自动生成64位安全API密钥
- 支持密钥启用/停用状态控制
- 支持密钥过期时间设置
- 支持重新生成密钥功能

### 2. 权限组控制
- 基于权限组的访问控制
- 支持多权限组配置
- 预设权限组：default、user、order、product、system
- 支持自定义权限组

### 3. IP白名单
- 支持单个IP地址白名单
- 支持CIDR格式IP段白名单
- 支持多IP配置（逗号分隔）
- 空白名单表示允许所有IP访问

### 4. 访问频率限制
- 基于小时的访问频率限制
- 使用Redis实现分布式限流
- 支持每个API Key独立配置限流值

### 5. 访问日志
- 完整的API访问日志记录
- 支持访问统计和趋势分析
- 支持日志导出和清理
- 异步记录，不影响接口性能

## 使用方法

### 1. 创建API Key

1. 登录系统管理后台
2. 进入"系统管理" -> "OpenAPI管理" -> "密钥管理"
3. 点击"新增"按钮
4. 填写密钥信息：
   - 密钥名称：便于识别的名称
   - 权限组：选择允许访问的权限组
   - IP白名单：限制访问的IP地址（可选）
   - 限流次数：每小时允许的请求次数
   - 过期时间：密钥的有效期（可选）
   - 备注：密钥说明
5. 保存后系统自动生成API密钥

### 2. 使用API Key访问接口

#### HTTP Header方式
```bash
curl -H "X-API-Key: your-api-key-here" \
     -H "Content-Type: application/json" \
     https://your-domain/openapi/user/list
```

#### 示例请求
```bash
# 获取用户列表
curl -X GET "http://localhost:8080/openapi/user/list?pageNum=1&pageSize=10" \
     -H "X-API-Key: demo123456789012345678901234567890123456789012345678901234567890"

# 获取用户详情
curl -X GET "http://localhost:8080/openapi/user/1" \
     -H "X-API-Key: demo123456789012345678901234567890123456789012345678901234567890"

# 创建新用户
curl -X POST "http://localhost:8080/openapi/user" \
     -H "X-API-Key: demo123456789012345678901234567890123456789012345678901234567890" \
     -H "Content-Type: application/json" \
     -d '{
       "userName": "apiuser",
       "nickName": "API用户",
       "email": "<EMAIL>"
     }'
```

### 3. 测试接口

系统提供了测试接口用于验证OpenAPI功能：

```bash
# Hello World接口
curl -X GET "http://localhost:8080/api/test/hello?name=World" \
     -H "X-API-Key: your-api-key"

# 获取当前时间
curl -X GET "http://localhost:8080/api/test/time" \
     -H "X-API-Key: your-api-key"

# Echo测试
curl -X POST "http://localhost:8080/api/test/echo" \
     -H "X-API-Key: your-api-key" \
     -H "Content-Type: application/json" \
     -d '{"message": "Hello OpenAPI"}'
```

## 开发指南

### 1. 为Controller添加OpenAPI支持

在Controller类上添加`@OpenAPI`注解：

```java
@RestController
@RequestMapping("/openapi/product")
@OpenAPI(group = "product", description = "商品管理相关接口")
public class OpenApiProductController {
    
    @GetMapping("/list")
    public AjaxResult list() {
        // 接口实现
    }
}
```

### 2. 权限组说明

- `default`: 默认权限组，基础功能
- `user`: 用户管理相关接口
- `order`: 订单管理相关接口
- `product`: 商品管理相关接口
- `system`: 系统管理相关接口

### 3. 错误码说明

- `401 Unauthorized`: API Key无效或缺失
- `403 Forbidden`: 权限不足或IP不在白名单
- `429 Too Many Requests`: 超出访问频率限制
- `500 Internal Server Error`: 服务器内部错误

## 安全建议

1. **密钥管理**
   - 定期更换API密钥
   - 不要在代码中硬编码API密钥
   - 使用环境变量或配置文件存储密钥

2. **访问控制**
   - 合理配置权限组，遵循最小权限原则
   - 配置IP白名单限制访问来源
   - 设置合理的访问频率限制

3. **监控审计**
   - 定期检查访问日志
   - 关注异常访问模式
   - 及时处理安全告警

## 故障排除

### 常见问题

1. **API Key验证失败**
   - 检查API Key是否正确
   - 检查API Key是否已过期
   - 检查API Key状态是否为启用

2. **权限不足**
   - 检查API Key是否有对应权限组的访问权限
   - 检查Controller的@OpenAPI注解配置

3. **IP访问被拒绝**
   - 检查IP白名单配置
   - 检查网络代理或负载均衡配置

4. **频率限制**
   - 检查API Key的限流配置
   - 适当调整访问频率或限流阈值

### 调试方法

1. 查看访问日志了解详细错误信息
2. 使用测试接口验证基本功能
3. 检查系统日志中的错误信息
4. 使用API Key测试功能验证密钥有效性

## 更新日志

### v1.0.0
- 初始版本发布
- 支持API Key认证
- 支持权限组管理
- 支持IP白名单
- 支持访问频率限制
- 支持访问日志记录