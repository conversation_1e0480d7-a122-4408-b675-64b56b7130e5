package com.leapmotor.sdl.web.controller.system;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.leapmotor.sdl.common.annotation.Log;
import com.leapmotor.sdl.common.core.controller.BaseController;
import com.leapmotor.sdl.common.core.domain.AjaxResult;
import com.leapmotor.sdl.common.enums.BusinessType;
import com.leapmotor.sdl.common.core.domain.OpenApiAccessLog;
import com.leapmotor.sdl.system.service.IOpenApiAccessLogService;
import com.leapmotor.sdl.common.utils.poi.ExcelUtil;
import com.leapmotor.sdl.common.core.page.TableDataInfo;

/**
 * OpenAPI访问日志Controller
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/system/openapi/log")
public class OpenApiAccessLogController extends BaseController
{
    @Autowired
    private IOpenApiAccessLogService openApiAccessLogService;

    /**
     * 查询OpenAPI访问日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:list')")
    @GetMapping("/list")
    public TableDataInfo list(OpenApiAccessLog openApiAccessLog)
    {
        startPage();
        List<OpenApiAccessLog> list = openApiAccessLogService.selectOpenApiAccessLogList(openApiAccessLog);
        return getDataTable(list);
    }

    /**
     * 导出OpenAPI访问日志列表
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:export')")
    @Log(title = "OpenAPI访问日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpenApiAccessLog openApiAccessLog)
    {
        List<OpenApiAccessLog> list = openApiAccessLogService.selectOpenApiAccessLogList(openApiAccessLog);
        ExcelUtil<OpenApiAccessLog> util = new ExcelUtil<OpenApiAccessLog>(OpenApiAccessLog.class);
        util.exportExcel(response, list, "OpenAPI访问日志数据");
    }

    /**
     * 获取OpenAPI访问日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(openApiAccessLogService.selectOpenApiAccessLogById(id));
    }

    /**
     * 新增OpenAPI访问日志
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:add')")
    @Log(title = "OpenAPI访问日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OpenApiAccessLog openApiAccessLog)
    {
        return toAjax(openApiAccessLogService.insertOpenApiAccessLog(openApiAccessLog));
    }

    /**
     * 修改OpenAPI访问日志
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:edit')")
    @Log(title = "OpenAPI访问日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OpenApiAccessLog openApiAccessLog)
    {
        return toAjax(openApiAccessLogService.updateOpenApiAccessLog(openApiAccessLog));
    }

    /**
     * 删除OpenAPI访问日志
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:remove')")
    @Log(title = "OpenAPI访问日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(openApiAccessLogService.deleteOpenApiAccessLogByIds(ids));
    }

    /**
     * 根据API密钥查询访问统计
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:query')")
    @GetMapping("/stats/{apiKey}")
    public AjaxResult getAccessStats(@PathVariable("apiKey") String apiKey)
    {
        List<OpenApiAccessLog> stats = openApiAccessLogService.selectAccessStatsByApiKey(apiKey);
        return success(stats);
    }

    /**
     * 清理过期的访问日志
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:remove')")
    @Log(title = "清理过期访问日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean/{days}")
    public AjaxResult cleanExpiredLogs(@PathVariable("days") int days)
    {
        int deletedCount = openApiAccessLogService.cleanExpiredAccessLogs(days);
        return success("清理完成，共删除" + deletedCount + "条记录");
    }
}