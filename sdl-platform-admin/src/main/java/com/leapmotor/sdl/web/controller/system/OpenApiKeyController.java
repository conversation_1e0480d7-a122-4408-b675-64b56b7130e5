package com.leapmotor.sdl.web.controller.system;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.leapmotor.sdl.common.annotation.Log;
import com.leapmotor.sdl.common.core.controller.BaseController;
import com.leapmotor.sdl.common.core.domain.AjaxResult;
import com.leapmotor.sdl.common.enums.BusinessType;
import com.leapmotor.sdl.common.core.domain.OpenApiKey;
import com.leapmotor.sdl.system.service.IOpenApiKeyService;
import com.leapmotor.sdl.common.utils.poi.ExcelUtil;
import com.leapmotor.sdl.common.core.page.TableDataInfo;

/**
 * OpenAPI密钥管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/system/openapi/key")
public class OpenApiKeyController extends BaseController
{
    @Autowired
    private IOpenApiKeyService openApiKeyService;

    /**
     * 查询OpenAPI密钥管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:list')")
    @GetMapping("/list")
    public TableDataInfo list(OpenApiKey openApiKey)
    {
        startPage();
        List<OpenApiKey> list = openApiKeyService.selectOpenApiKeyList(openApiKey);
        return getDataTable(list);
    }

    /**
     * 导出OpenAPI密钥管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:export')")
    @Log(title = "OpenAPI密钥管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OpenApiKey openApiKey)
    {
        List<OpenApiKey> list = openApiKeyService.selectOpenApiKeyList(openApiKey);
        ExcelUtil<OpenApiKey> util = new ExcelUtil<OpenApiKey>(OpenApiKey.class);
        util.exportExcel(response, list, "OpenAPI密钥管理数据");
    }

    /**
     * 获取OpenAPI密钥管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(openApiKeyService.selectOpenApiKeyById(id));
    }

    /**
     * 新增OpenAPI密钥管理
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:add')")
    @Log(title = "OpenAPI密钥管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OpenApiKey openApiKey)
    {
        OpenApiKey result = openApiKeyService.insertOpenApiKey(openApiKey);
        if (result != null && result.getApiKey() != null) {
            return success("新增成功", result);
        }
        return error("新增失败");
    }

    /**
     * 修改OpenAPI密钥管理
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:edit')")
    @Log(title = "OpenAPI密钥管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OpenApiKey openApiKey)
    {
        return toAjax(openApiKeyService.updateOpenApiKey(openApiKey));
    }

    /**
     * 删除OpenAPI密钥管理
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:remove')")
    @Log(title = "OpenAPI密钥管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(openApiKeyService.deleteOpenApiKeyByIds(ids));
    }

    /**
     * 重新生成API密钥
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:edit')")
    @Log(title = "重新生成API密钥", businessType = BusinessType.UPDATE)
    @PutMapping("/regenerate/{id}")
    public AjaxResult regenerateApiKey(@PathVariable("id") Long id)
    {
        String newApiKey = openApiKeyService.regenerateApiKey(id);
        if (newApiKey != null)
        {
            return success("重新生成成功", newApiKey);
        }
        else
        {
            return error("重新生成失败");
        }
    }

    /**
     * 修改API密钥状态
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:edit')")
    @Log(title = "修改API密钥状态", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody OpenApiKey openApiKey)
    {
        return toAjax(openApiKeyService.updateApiKeyStatus(openApiKey.getId(), openApiKey.getStatus()));
    }

    /**
     * 获取可用的权限组列表
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:query')")
    @GetMapping("/groups")
    public AjaxResult getAvailableGroups()
    {
        // 返回系统中可用的权限组列表
        String[] groups = {"应用管理", "项目管理", "漏洞管理","资产管理"};
        return success(groups);
    }

    /**
     * 测试API密钥有效性
     */
    @PreAuthorize("@ss.hasPermi('system:openapi:key:query')")
    @PostMapping("/test/{apiKey}")
    public AjaxResult testApiKey(@PathVariable("apiKey") String apiKey)
    {
        OpenApiKey openApiKeyInfo = openApiKeyService.selectOpenApiKeyByApiKey(apiKey);
        if (openApiKeyInfo == null)
        {
            return error("API密钥不存在");
        }
        
        if (!"0".equals(openApiKeyInfo.getStatus()))
        {
            return error("API密钥已停用");
        }
        
        if (openApiKeyInfo.getExpireTime() != null && 
            openApiKeyInfo.getExpireTime().before(new java.util.Date()))
        {
            return error("API密钥已过期");
        }
        
        return success("API密钥有效");
    }
}