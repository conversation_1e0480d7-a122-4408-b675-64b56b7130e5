# 内网环境开发
spring:
  data:
    redis:
      host: *************  # 内网Redis地址，请根据实际情况修改
      port: 6379
      database: 0
      password: CompanyRedisPassword  # 内网Redis密码，请根据实际情况修改
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms
  
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      master:
        url: ************************************************************************************************************************************************  # 内网MySQL地址，请根据实际情况修改
        username: root
        password: CompanyMySQLPassword  # 内网MySQL密码，请根据实际情况修改
      slave:
        enabled: false
        url: 
        username: 
        password: 
      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 60000
      connectTimeout: 30000
      socketTimeout: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter: 
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
