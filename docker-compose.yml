services:
  # 后端服务
  sdl-backend:
    build:
      context: .
      dockerfile: docker/backend.Dockerfile
    container_name: sdl-backend
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
      JAVA_OPTS: "-Xms512m -Xmx1024m"
    ports:
      - "8080:8080"
    volumes:
      - backend_uploads:/app/uploadPath
      - backend_logs:/app/logs
    networks:
      - sdl-network

  # 前端服务
  sdl-frontend:
    build:
      context: .
      dockerfile: docker/frontend.Dockerfile
    container_name: sdl-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    depends_on:
      - sdl-backend
    networks:
      - sdl-network

# 网络配置
networks:
  sdl-network:
    driver: bridge

# 数据卷配置
volumes:
  backend_uploads:
    driver: local
  backend_logs:
    driver: local