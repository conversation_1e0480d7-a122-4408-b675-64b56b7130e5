######################################################################
# Build Tools

.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

target/
!.mvn/wrapper/maven-wrapper.jar

######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml

### NetBeans ###
nbproject/private/
build/*
nbbuild/
dist/
nbdist/
.nb-gradle/

######################################################################
# Others
*.log
*.xml.versionsBackup
*.swp

!*/build/*.java
!*/build/*.html
!*/build/*.xml

.claude
CLAUDE.md

.vscode

######################################################################
# Docker

# Docker 构建产物目录
docker/deploy/

# Docker 镜像构建缓存
.dockerignore.bak
docker-compose.override.yml

# Docker 卷数据
.docker/

# Docker 构建上下文临时文件
.docker-build/

# Docker 镜像 tar 文件
*.tar
*.tar.gz
*.tar.bz2
*.tar.xz

# Docker 日志
docker-compose.log
docker.log

# Docker 环境变量文件
.env.docker
.env.local
.env.production
.env.staging

# Docker 多阶段构建产物
**/target/
**/dist/
**/node_modules/
**/.vite/
**/.cache/

# Docker 构建日志
docker-build.log
build.log