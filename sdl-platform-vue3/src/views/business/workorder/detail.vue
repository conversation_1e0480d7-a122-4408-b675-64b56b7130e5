<template>
  <div class="app-container">
    <el-page-header @back="goBack" :content="pageTitle" />
    
    <el-card class="box-card" style="margin-top: 20px;">
      <!-- 工单详细信息 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="工单ID">
          {{ workorderInfo.ticketId || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="工单标题">
          {{ workorderInfo.ticketTitle || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="测试类型">
          <dict-tag :options="sdl_test_type" :value="workorderInfo.ticketType"/>
        </el-descriptions-item>
        <el-descriptions-item label="工单状态">
          <dict-tag :options="sdl_ticket_status" :value="workorderInfo.ticketStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="所属项目">
          {{ workorderInfo.projectName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="系统名称">
          {{ workorderInfo.systemName || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="项目经理">
          {{ workorderInfo.projectManager || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="软件工程师">
          {{ workorderInfo.softwareEngineer || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="测试申请人">
          {{ workorderInfo.testApplicant || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="安全对接人">
          {{ workorderInfo.securityRepresentative || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="当前处理人">
          {{ workorderInfo.handler || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="优先级">
          <dict-tag :options="sdl_priority" :value="workorderInfo.priority"/>
        </el-descriptions-item>
        <el-descriptions-item label="期望完成时间">
          {{ parseTime(workorderInfo.expectFinishTime, '{y}-{m}-{d}') || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="实际完成时间">
          {{ parseTime(workorderInfo.actualFinishTime, '{y}-{m}-{d}') || '-' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ parseTime(workorderInfo.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="更新时间">
          {{ parseTime(workorderInfo.updateTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="测试环境地址" :span="2">
          <div style="word-break: break-all;">
            {{ workorderInfo.testEnv || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="测试账号密码" :span="2">
          <div style="word-break: break-all;">
            {{ workorderInfo.testAccount || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="代码仓库地址" :span="2">
          <div style="word-break: break-all;">
            <el-link v-if="workorderInfo.codeRepo" :href="workorderInfo.codeRepo" target="_blank" type="primary">
              {{ workorderInfo.codeRepo }}
            </el-link>
            <span v-else>-</span>
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="测试范围" :span="2">
          <div style="white-space: pre-wrap;">
            {{ workorderInfo.trestScope || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="附件信息" :span="2">
          <div style="white-space: pre-wrap;">
            {{ workorderInfo.attachment || '-' }}
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          <div style="white-space: pre-wrap;">
            {{ workorderInfo.remark || '-' }}
          </div>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup name="WorkorderDetail">
import { getWorkorder } from "@/api/business/workorder.js"
import { ref, getCurrentInstance, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const { sdl_ticket_status, sdl_priority, sdl_test_type } = proxy.useDict('sdl_ticket_status', 'sdl_priority', 'sdl_test_type')

const workorderInfo = ref({})
const pageTitle = ref('工单详情')

/** 返回上一页 */
function goBack() {
  router.back()
}

/** 获取工单详情 */
function getWorkorderDetail() {
  const ticketId = route.query.id
  if (ticketId) {
    getWorkorder(ticketId).then(response => {
      workorderInfo.value = response.data
      pageTitle.value = `${workorderInfo.value.ticketTitle}`
    }).catch(() => {
      proxy.$modal.msgError('获取工单详情失败')
      router.back()
    })
  } else {
    proxy.$modal.msgError('缺少工单ID参数')
    router.back()
  }
}

onMounted(() => {
  getWorkorderDetail()
})
</script>

<style scoped>
.box-card {
  min-height: 500px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}
</style>