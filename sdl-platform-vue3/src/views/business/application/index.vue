<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="应用名称" prop="appName">
            <el-input
              v-model="queryParams.appName"
              placeholder="请输入应用名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="应用类型" prop="appType">
            <el-select v-model="queryParams.appType" placeholder="请选择应用类型" clearable style="width: 100%">
              <el-option
                v-for="dict in sdl_app_type"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="业务领域" prop="businessDomain">
            <el-select v-model="queryParams.businessDomain" placeholder="请选择业务领域" clearable style="width: 100%">
              <el-option
                v-for="dict in sdl_business_domian"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="所属部门" prop="department">
            <el-input
              v-model="queryParams.department"
              placeholder="请输入所属部门"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="责任人" prop="responsiblePerson">
            <el-input
              v-model="queryParams.responsiblePerson"
              placeholder="请输入责任人"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="安全等级" prop="securityLevel">
            <el-select v-model="queryParams.securityLevel" placeholder="请选择安全等级" clearable style="width: 100%">
              <el-option
                v-for="dict in sdl_security_level"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建时间" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item style="margin-bottom: 0px; margin-right: 0px;">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery" style="margin-right: 5px;">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['business:application:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['business:application:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['business:application:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['business:application:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="applicationList" @selection-change="handleSelectionChange" fit style="width: 100%">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="应用名称" align="center" prop="appName" show-overflow-tooltip />
      <el-table-column label="应用类型" align="center" prop="appType">
        <template #default="scope">
          <dict-tag :options="sdl_app_type" :value="scope.row.appType"/>
        </template>
      </el-table-column>
      <el-table-column label="业务领域" align="center" prop="businessDomain">
        <template #default="scope">
          <dict-tag :options="sdl_business_domian" :value="scope.row.businessDomain"/>
        </template>
      </el-table-column>
      <el-table-column label="所属部门" align="center" prop="department"/>
      <el-table-column label="责任人" align="center" prop="responsiblePerson"/>
      <el-table-column label="安全代表" align="center" prop="securityRepresentative"/>
      <el-table-column label="安全等级" align="center" prop="securityLevel">
        <template #default="scope">
          <dict-tag :options="sdl_security_level" :value="scope.row.securityLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="200">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)" v-hasPermi="['business:application:query']">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['business:application:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['business:application:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改应用对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="applicationRef" :model="form" :rules="rules" label-width="100px">
        <el-divider content-position="left">基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="应用名称" prop="appName">
              <el-input v-model="form.appName" placeholder="请输入应用名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属部门" prop="department">
              <el-input v-model="form.department" placeholder="请输入所属部门" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="责任人" prop="responsiblePerson">
              <el-input v-model="form.responsiblePerson" placeholder="请输入责任人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="应用类型" prop="appType">
              <el-select v-model="form.appType" placeholder="请选择应用类型" style="width: 100%">
                <el-option
                  v-for="dict in sdl_app_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="业务领域" prop="businessDomain">
              <el-select v-model="form.businessDomain" placeholder="请选择业务领域" style="width: 100%">
                <el-option
                  v-for="dict in sdl_business_domian"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="安全代表" prop="securityRepresentative">
              <el-input v-model="form.securityRepresentative" placeholder="请输入安全代表" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开发类型" prop="developmentType">
              <el-select v-model="form.developmentType" placeholder="请选择开发类型" style="width: 100%">
                <el-option
                  v-for="dict in sdl_dev_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="应用成员" prop="appMenbers">
              <el-input v-model="form.appMenbers" placeholder="请输入应用成员，多个成员用逗号分隔" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left">定级信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="20">
            <el-form-item label="用户类型" prop="userType">
              <el-checkbox-group v-model="form.userType">
                <el-checkbox
                  v-for="dict in sdl_user_type"
                  :key="dict.value"
                  :label="dict.value">
                  {{dict.label}}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="访问路径" prop="accessPath">
              <el-checkbox-group v-model="form.accessPath">
                <el-checkbox
                  v-for="dict in sdl_access_path"
                  :key="dict.value"
                  :label="dict.value">
                  {{dict.label}}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="数据敏感度" prop="dataSensitivity">
              <el-select v-model="form.dataSensitivity" placeholder="请选择数据敏感度" style="width: 100%">
                <el-option
                  v-for="dict in sdl_data_sensitivity"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
           <el-col :span="8">
            <el-form-item label="业务重要性" prop="businessImportance">
              <el-select v-model="form.businessImportance" placeholder="请选择业务重要性" style="width: 100%">
                <el-option
                  v-for="dict in sdl_business_importance"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="系统依赖性" prop="systemDependency">
              <el-select v-model="form.systemDependency" placeholder="请选择系统依赖性" style="width: 100%">
                <el-option
                  v-for="dict in sdl_system_dependency"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="安全等级" prop="securityLevel">
              <el-select v-model="form.securityLevel" placeholder="请选择安全等级" style="width: 100%">
                <el-option
                  v-for="dict in sdl_security_level"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col> -->
        </el-row>
        <el-divider content-position="left">其它信息</el-divider>
        <el-row :gutter="20">
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="开发语言" prop="developmentLanguage">
              <el-checkbox-group v-model="form.developmentLanguage">
                <el-checkbox
                  v-for="dict in sdl_dev_language"
                  :key="dict.value"
                  :label="dict.value">
                  {{dict.label}}
                </el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="RTO" prop="recoveryTimeObjective">
              <el-input v-model="form.recoveryTimeObjective" placeholder="请输入恢复时间目标" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="RPO" prop="recoveryPointObjective">
              <el-input v-model="form.recoveryPointObjective" placeholder="请输入恢复时间点目标" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Application">
import { listApplication, getApplication, delApplication, addApplication, updateApplication } from "@/api/business/application"
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'

const { proxy } = getCurrentInstance()
const router = useRouter()
const { sdl_user_type, sdl_data_sensitivity, sdl_dev_type, sdl_security_level, sdl_business_domian, sdl_dev_language, sdl_business_importance, sdl_app_type, sdl_system_dependency, sdl_access_path } = proxy.useDict('sdl_user_type', 'sdl_data_sensitivity', 'sdl_dev_type', 'sdl_security_level', 'sdl_business_domian', 'sdl_dev_language', 'sdl_business_importance', 'sdl_app_type', 'sdl_system_dependency', 'sdl_access_path')

const applicationList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    appName: null,
    appType: null,
    businessDomain: null,
    department: null,
    responsiblePerson: null,
    securityLevel: null,
    startCreateTime: null,
    endCreateTime: null,
  },
  rules: {
    appName: [
      { required: true, message: "应用名称不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 日期范围选择处理 */
function handleDateRangeChange(value) {
  if (value && value.length === 2) {
    queryParams.value.startCreateTime = value[0]
    queryParams.value.endCreateTime = value[1]
  } else {
    queryParams.value.startCreateTime = null
    queryParams.value.endCreateTime = null
  }
}

/** 查看按钮操作 */
function handleView(row) {
  const appId = row.appId
  router.push({
    path: '/business/application/detail',
    query: { id: appId }
  })
}

/** 查询应用列表 */
function getList() {
  loading.value = true
  listApplication(queryParams.value).then(response => {
    applicationList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    appId: null,
    appName: null,
    appType: null,
    businessDomain: null,
    department: null,
    responsiblePerson: null,
    securityRepresentative: null,
    securityLevel: null,
    userType: [],
    developmentLanguage: [],
    developmentType: null,
    recoveryTimeObjective: null,
    recoveryPointObjective: null,
    accessPath: [],
    dataSensitivity: null,
    businessImportance: null,
    systemDependency: null,
    appMenbers: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    remark: null
  }
  proxy.resetForm("applicationRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  queryParams.value.startCreateTime = null
  queryParams.value.endCreateTime = null
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.appId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加应用"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _appId = row.appId || ids.value
  getApplication(_appId).then(response => {
    form.value = response.data
    form.value.userType = form.value.userType.split(",")
    form.value.developmentLanguage = form.value.developmentLanguage.split(",")
    form.value.accessPath = form.value.accessPath.split(",")
    open.value = true
    title.value = "修改应用"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["applicationRef"].validate(valid => {
    if (valid) {
      form.value.userType = form.value.userType.join(",")
      form.value.developmentLanguage = form.value.developmentLanguage.join(",")
      form.value.accessPath = form.value.accessPath.join(",")
      if (form.value.appId != null) {
        updateApplication(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addApplication(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _appIds = row.appId || ids.value
  proxy.$modal.confirm('是否确认删除应用编号为"' + _appIds + '"的数据项？').then(function() {
    return delApplication(_appIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('business/application/export', {
    ...queryParams.value
  }, `application_${new Date().getTime()}.xlsx`)
}

getList()
</script>
