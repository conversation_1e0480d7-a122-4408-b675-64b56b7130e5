<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="API密钥" prop="apiKey">
            <el-input
              v-model="queryParams.apiKey"
              placeholder="请输入API密钥"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="请求URI" prop="requestUri">
            <el-input
              v-model="queryParams.requestUri"
              placeholder="请输入请求URI"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="客户端IP" prop="clientIp">
            <el-input
              v-model="queryParams.clientIp"
              placeholder="请输入客户端IP"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="响应状态" prop="responseStatus">
            <el-select v-model="queryParams.responseStatus" placeholder="请选择响应状态" clearable style="width: 100%">
              <el-option label="成功 (200)" value="200" />
              <el-option label="未授权 (401)" value="401" />
              <el-option label="禁止访问 (403)" value="403" />
              <el-option label="限流 (429)" value="429" />
              <el-option label="服务器错误 (500)" value="500" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="请求时间" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleDateRangeChange"
            />
          </el-form-item>
        </el-col>

<!--        占位，把搜索、重置按钮推到右侧-->
        <el-col :span="12"></el-col>

        <el-col :span="6">
          <el-form-item style="margin-right: 5px">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>

      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="accessLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" type="index" width="50" align="center" />
      <el-table-column label="API密钥" align="center" prop="apiKey" show-overflow-tooltip width="200">
        <template #default="scope">
          <span>{{ scope.row.apiKey ? scope.row.apiKey.substring(0, 16) + '***' : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请求URI" align="center" prop="requestUri" show-overflow-tooltip />
      <el-table-column label="请求方法" align="center" prop="requestMethod" width="100">
        <template #default="scope">
          <el-tag :type="getMethodTagType(scope.row.requestMethod)">{{ scope.row.requestMethod }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="客户端IP" align="center" prop="clientIp" width="140" />
      <el-table-column label="响应状态" align="center" prop="responseStatus" width="100">
        <template #default="scope">
          <el-tag :type="getStatusTagType(scope.row.responseStatus)">{{ scope.row.responseStatus }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="响应时间" align="center" prop="responseTime" width="100">
        <template #default="scope">
          <span>{{ scope.row.responseTime }}ms</span>
        </template>
      </el-table-column>
      <el-table-column label="错误信息" align="center" prop="errorMessage" show-overflow-tooltip />
      <el-table-column label="用户代理" align="center" prop="userAgent" show-overflow-tooltip />
      <el-table-column label="请求时间" align="center" prop="requestTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.requestTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="160">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleView(scope.row)">详情</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:openapi:log:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 访问日志详情对话框 -->
    <el-dialog title="访问日志详情" v-model="open" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="日志ID">{{ form.logId }}</el-descriptions-item>
        <el-descriptions-item label="API密钥">
          <span v-if="form.apiKey">{{ form.apiKey.substring(0, 16) }}***</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="请求URI">{{ form.requestUri }}</el-descriptions-item>
        <el-descriptions-item label="请求方法">
          <el-tag :type="getMethodTagType(form.requestMethod)">{{ form.requestMethod }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="客户端IP">{{ form.clientIp }}</el-descriptions-item>
        <el-descriptions-item label="响应状态">
          <el-tag :type="getStatusTagType(form.responseStatus)">{{ form.responseStatus }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="响应时间">{{ form.responseTime }}ms</el-descriptions-item>
        <el-descriptions-item label="请求时间">{{ parseTime(form.requestTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</el-descriptions-item>
        <el-descriptions-item label="用户代理" :span="2">{{ form.userAgent || '-' }}</el-descriptions-item>
        <el-descriptions-item label="错误信息" :span="2">
          <el-text type="danger" v-if="form.errorMessage">{{ form.errorMessage }}</el-text>
          <span v-else>-</span>
        </el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OpenApiAccessLog">
import { listOpenApiAccessLog, delOpenApiAccessLog, cleanOpenApiAccessLog } from "@/api/system/openapi.js"
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

const accessLogList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const multiple = ref(true)
const total = ref(0)
const dateRange = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    apiKey: null,
    requestUri: null,
    clientIp: null,
    responseStatus: null,
    beginTime: null,
    endTime: null
  }
})

const { queryParams, form } = toRefs(data)

/** 日期范围变化处理 */
function handleDateRangeChange(value) {
  if (value && value.length === 2) {
    queryParams.value.beginTime = value[0]
    queryParams.value.endTime = value[1]
  } else {
    queryParams.value.beginTime = null
    queryParams.value.endTime = null
  }
}

/** 查询访问日志列表 */
function getList() {
  loading.value = true
  listOpenApiAccessLog(queryParams.value).then(response => {
    accessLogList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    logId: null,
    apiKey: null,
    requestUri: null,
    requestMethod: null,
    clientIp: null,
    userAgent: null,
    responseStatus: null,
    responseTime: null,
    requestTime: null,
    errorMessage: null
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  queryParams.value.beginTime = null
  queryParams.value.endTime = null
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.logId)
  multiple.value = !selection.length
}

/** 详情按钮操作 */
function handleView(row) {
  reset()
  form.value = row
  open.value = true
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _logIds = row.logId || ids.value
  proxy.$modal.confirm('是否确认删除访问日志编号为"' + _logIds + '"的数据项？').then(function() {
    return delOpenApiAccessLog(_logIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 清空按钮操作 */
function handleClean() {
  proxy.$modal.confirm('是否确认清空所有访问日志数据项？').then(function() {
    return cleanOpenApiAccessLog()
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("清空成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/openapi/log/export', {
    ...queryParams.value
  }, `openapi_access_log_${new Date().getTime()}.xlsx`)
}

/** 获取方法标签类型 */
function getMethodTagType(method) {
  const typeMap = {
    'GET': 'info',
    'POST': 'success',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'primary'
  }
  return typeMap[method] || 'info'
}

/** 获取状态标签类型 */
function getStatusTagType(status) {
  if (status >= 200 && status < 300) {
    return 'success'
  } else if (status >= 400 && status < 500) {
    return 'warning'
  } else if (status >= 500) {
    return 'danger'
  } else {
    return 'info'
  }
}

getList()
</script>