<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-form-item label="密钥名称" prop="name">
            <el-input
              v-model="queryParams.name"
              placeholder="请输入密钥名称"
              clearable
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100%">
              <el-option label="正常" value="0"/>
              <el-option label="停用" value="1"/>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="创建时间" prop="dateRange">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              @change="handleDateRangeChange"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item style="margin-right: 5px">
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:openapi:key:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:openapi:key:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:openapi:key:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:openapi:key:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="openApiKeyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="密钥名称" align="center" prop="name" show-overflow-tooltip />
      <el-table-column label="API密钥" align="center" prop="apiKey" show-overflow-tooltip width="240">
        <template #default="scope">
          <span>{{ scope.row.apiKey?.substring(0, 20) }}...</span>
          <el-button
            link
            type="primary"
            icon="DocumentCopy"
            @click="copyApiKey(scope.row.apiKey)"
            title="复制完整密钥"
          ></el-button>
        </template>
      </el-table-column>
      <el-table-column label="权限组" align="center" prop="allowedGroups" show-overflow-tooltip width="160" />
      <el-table-column label="状态" align="center" prop="status" width="100">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="0"
            inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="限流次数/小时" align="center" prop="rateLimit" width="140" />
      <el-table-column label="总访问次数" align="center" prop="totalAccessCount" width="120" />
      <el-table-column label="最后访问时间" align="center" prop="lastAccessTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.lastAccessTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="过期时间" align="center" prop="expireTime" width="160">
        <template #default="scope">
          <span>{{ parseTime(scope.row.expireTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:openapi:key:edit']">修改</el-button>
          <el-button link type="primary" icon="Refresh" @click="handleRegenerate(scope.row)" v-hasPermi="['system:openapi:key:edit']">重置</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:openapi:key:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改OpenAPI密钥对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="openApiKeyRef" :model="form" :rules="rules" label-width="100px">
        <el-divider content-position="left">基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="密钥名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入密钥名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select v-model="form.status" placeholder="请选择状态" style="width: 100%">
                <el-option label="正常" value="0" />
                <el-option label="停用" value="1" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="权限组" prop="allowedGroups">
              <el-select
                v-model="selectedGroups"
                multiple
                filterable
                allow-create
                placeholder="请选择或输入权限组"
                style="width: 100%"
              >
                <el-option
                  v-for="group in availableGroups"
                  :key="group"
                  :label="group"
                  :value="group"
                ></el-option>
              </el-select>
              <div class="form-tip">多个权限组用逗号分隔，可选择预设组或自定义输入</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="left">安全配置</el-divider>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="限流次数" prop="rateLimit">
              <el-input-number
                v-model="form.rateLimit"
                :min="1"
                :max="10000"
                placeholder="每小时请求次数限制"
                style="width: 100%"
              />
              <div class="form-tip">每小时允许的最大请求次数</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="过期时间" prop="expireTime">
              <el-date-picker
                v-model="form.expireTime"
                type="datetime"
                placeholder="选择过期时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
              />
              <div class="form-tip">留空表示永不过期</div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="IP白名单" prop="ipWhitelist">
              <el-input
                v-model="form.ipWhitelist"
                type="textarea"
                placeholder="请输入IP白名单，多个IP用逗号分隔，支持CIDR格式，如：***********,10.0.0.0/24"
                :rows="3"
              />
              <div class="form-tip">留空表示允许所有IP访问</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="4" placeholder="请输入备注信息" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- API密钥显示对话框 -->
    <el-dialog title="API密钥" v-model="apiKeyDialogVisible" width="600px" append-to-body>
      <div style="margin-bottom: 20px;">
        <el-alert
          title="请妥善保管API密钥"
          description="API密钥具有完整的接口访问权限，请勿泄露给他人。建议定期更换密钥以确保安全。"
          type="warning"
          :closable="false"
        />
      </div>
      <el-form label-width="80px">
        <el-form-item label="API密钥">
          <el-input
            v-model="displayApiKey"
            readonly
            :rows="3"
            style="font-family: monospace"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="copyApiKey(displayApiKey)">复制密钥</el-button>
          <el-button @click="apiKeyDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OpenApiKey">
import { listOpenApiKey, getOpenApiKey, delOpenApiKey, addOpenApiKey, updateOpenApiKey, changeStatus, regenerateApiKey, getAvailableGroups } from "@/api/system/openapi.js"
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()

const openApiKeyList = ref([])
const open = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const title = ref("")
const dateRange = ref([])
const apiKeyDialogVisible = ref(false)
const displayApiKey = ref("")
const availableGroups = ref([])
const selectedGroups = ref([])

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    status: null,
    startCreateTime: null,
    endCreateTime: null,
  },
  rules: {
    name: [
      { required: true, message: "密钥名称不能为空", trigger: "blur" }
    ],
  }
})

const { queryParams, form, rules } = toRefs(data)

/** 日期范围选择处理 */
function handleDateRangeChange(value) {
  if (value && value.length === 2) {
    queryParams.value.startCreateTime = value[0]
    queryParams.value.endCreateTime = value[1]
  } else {
    queryParams.value.startCreateTime = null
    queryParams.value.endCreateTime = null
  }
}

/** 查询OpenAPI密钥列表 */
function getList() {
  loading.value = true
  listOpenApiKey(queryParams.value).then(response => {
    openApiKeyList.value = response.rows
    total.value = response.total
    loading.value = false
  })
}

/** 获取可用权限组 */
function getAvailableGroupsList() {
  getAvailableGroups().then(response => {
    availableGroups.value = response.data || ["default"]
  })
}

// 取消按钮
function cancel() {
  open.value = false
  reset()
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    allowedGroups: null,
    ipWhitelist: null,
    rateLimit: 100,
    expireTime: null,
    status: "0",
    remark: null
  }
  selectedGroups.value = []
  proxy.resetForm("openApiKeyRef")
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = []
  proxy.resetForm("queryRef")
  handleQuery()
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 新增按钮操作 */
function handleAdd() {
  reset()
  open.value = true
  title.value = "添加OpenAPI密钥"
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset()
  const _id = row.id || ids.value
  getOpenApiKey(_id).then(response => {
    form.value = response.data
    selectedGroups.value = form.value.allowedGroups ? form.value.allowedGroups.split(',') : []
    open.value = true
    title.value = "修改OpenAPI密钥"
  })
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["openApiKeyRef"].validate(valid => {
    if (valid) {
      // 处理权限组
      form.value.allowedGroups = selectedGroups.value.join(',')
      
      if (form.value.id != null) {
        updateOpenApiKey(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功")
          open.value = false
          getList()
        })
      } else {
        addOpenApiKey(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功")
          open.value = false
          getList()
          // 显示生成的API密钥
          if (response.data && response.data.apiKey) {
            displayApiKey.value = response.data.apiKey
            apiKeyDialogVisible.value = true
          }
        })
      }
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value
  proxy.$modal.confirm('是否确认删除OpenAPI密钥编号为"' + _ids + '"的数据项？').then(function() {
    return delOpenApiKey(_ids)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => {})
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/openapi/key/export', {
    ...queryParams.value
  }, `openapi_key_${new Date().getTime()}.xlsx`)
}

/** 状态修改 */
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"密钥吗？').then(function() {
    return changeStatus(row)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function() {
    row.status = row.status === "0" ? "1" : "0"
  })
}

/** 重新生成API密钥 */
function handleRegenerate(row) {
  proxy.$modal.confirm('重新生成密钥将使旧密钥失效，确认要重新生成"' + row.name + '"的API密钥吗？').then(function() {
    return regenerateApiKey(row.id)
  }).then((response) => {
    proxy.$modal.msgSuccess("重新生成成功")
    getList()
    // 显示新生成的API密钥
    displayApiKey.value = response.data
    apiKeyDialogVisible.value = true
  }).catch(() => {})
}

/** 复制API密钥 */
function copyApiKey(apiKey) {
  const textArea = document.createElement('textarea')
  textArea.value = apiKey
  document.body.appendChild(textArea)
  textArea.select()
  document.execCommand('copy')
  document.body.removeChild(textArea)
  proxy.$modal.msgSuccess("API密钥已复制到剪贴板")
}

getList()
getAvailableGroupsList()
</script>

<style scoped>
.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>