import request from '@/utils/request'

// 查询测试工单列表
export function listWorkorder(query) {
  return request({
    url: '/business/workorder/list',
    method: 'get',
    params: query
  })
}

// 查询测试工单详细
export function getWorkorder(ticketId) {
  return request({
    url: '/business/workorder/' + ticketId,
    method: 'get'
  })
}

// 新增测试工单
export function addWorkorder(data) {
  return request({
    url: '/business/workorder',
    method: 'post',
    data: data
  })
}

// 修改测试工单
export function updateWorkorder(data) {
  return request({
    url: '/business/workorder',
    method: 'put',
    data: data
  })
}

// 删除测试工单
export function delWorkorder(ticketId) {
  return request({
    url: '/business/workorder/' + ticketId,
    method: 'delete'
  })
}
