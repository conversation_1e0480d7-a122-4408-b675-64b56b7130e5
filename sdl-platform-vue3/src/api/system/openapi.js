import request from '@/utils/request'

// 查询OpenAPI密钥管理列表
export function listOpenApiKey(query) {
  return request({
    url: '/system/openapi/key/list',
    method: 'get',
    params: query
  })
}

// 查询OpenAPI密钥管理详细
export function getOpenApiKey(id) {
  return request({
    url: '/system/openapi/key/' + id,
    method: 'get'
  })
}

// 新增OpenAPI密钥管理
export function addOpenApiKey(data) {
  return request({
    url: '/system/openapi/key',
    method: 'post',
    data: data
  })
}

// 修改OpenAPI密钥管理
export function updateOpenApiKey(data) {
  return request({
    url: '/system/openapi/key',
    method: 'put',
    data: data
  })
}

// 删除OpenAPI密钥管理
export function delOpenApiKey(id) {
  return request({
    url: '/system/openapi/key/' + id,
    method: 'delete'
  })
}

// 修改OpenAPI密钥状态
export function changeStatus(data) {
  return request({
    url: '/system/openapi/key/changeStatus',
    method: 'put',
    data: data
  })
}

// 重新生成API密钥
export function regenerateApiKey(id) {
  return request({
    url: '/system/openapi/key/regenerate/' + id,
    method: 'put'
  })
}

// 获取可用权限组
export function getAvailableGroups() {
  return request({
    url: '/system/openapi/key/groups',
    method: 'get'
  })
}

// 测试API密钥
export function testApiKey(apiKey) {
  return request({
    url: '/system/openapi/key/test/' + apiKey,
    method: 'post'
  })
}

// 查询OpenAPI访问日志列表
export function listOpenApiAccessLog(query) {
  return request({
    url: '/system/openapi/log/list',
    method: 'get',
    params: query
  })
}

// 查询OpenAPI访问日志详细
export function getOpenApiAccessLog(id) {
  return request({
    url: '/system/openapi/log/' + id,
    method: 'get'
  })
}

// 删除OpenAPI访问日志
export function delOpenApiAccessLog(id) {
  return request({
    url: '/system/openapi/log/' + id,
    method: 'delete'
  })
}

// 根据API密钥查询访问统计
export function getAccessStatsByApiKey(apiKey) {
  return request({
    url: '/system/openapi/log/stats/' + apiKey,
    method: 'get'
  })
}

// 清空OpenAPI访问日志
export function cleanOpenApiAccessLog() {
  return request({
    url: '/system/openapi/log/clean',
    method: 'delete'
  })
}