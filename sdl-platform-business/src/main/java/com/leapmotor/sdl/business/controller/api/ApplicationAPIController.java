package com.leapmotor.sdl.business.controller.api;

import com.leapmotor.sdl.business.domain.Application;
import com.leapmotor.sdl.business.service.IApplicationService;
import com.leapmotor.sdl.common.annotation.OpenAPI;
import com.leapmotor.sdl.common.core.controller.BaseController;
import com.leapmotor.sdl.common.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * 应用管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/api/application")
@OpenAPI(group = "application", description = "应用相关接口")
@Tag(name = "Application Management", description = "应用管理相关接口")
public class ApplicationAPIController extends BaseController {
    @Autowired
    private IApplicationService applicationService;

    @GetMapping("/list")

    public TableDataInfo list(Application application) {
        startPage();
        List<Application> list = applicationService.selectApplicationList(application);
        return getDataTable(list);
    }
}
