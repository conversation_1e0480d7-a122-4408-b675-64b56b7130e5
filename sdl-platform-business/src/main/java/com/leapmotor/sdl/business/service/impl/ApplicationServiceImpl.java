package com.leapmotor.sdl.business.service.impl;

import java.util.List;
import com.leapmotor.sdl.common.utils.DateUtils;
import com.leapmotor.sdl.common.utils.uuid.IdUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.leapmotor.sdl.business.mapper.ApplicationMapper;
import com.leapmotor.sdl.business.domain.Application;
import com.leapmotor.sdl.business.service.IApplicationService;

/**
 * 应用Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
@Service
public class ApplicationServiceImpl implements IApplicationService 
{
    @Autowired
    private ApplicationMapper applicationMapper;

    /**
     * 查询应用
     * 
     * @param appId 应用主键
     * @return 应用
     */
    @Override
    public Application selectApplicationByAppId(Long appId)
    {
        return applicationMapper.selectApplicationByAppId(appId);
    }

    /**
     * 查询应用列表
     * 
     * @param application 应用
     * @return 应用
     */
    @Override
    public List<Application> selectApplicationList(Application application)
    {
        return applicationMapper.selectApplicationList(application);
    }

    /**
     * 新增应用
     * 
     * @param application 应用
     * @return 结果
     */
    @Override
    public int insertApplication(Application application)
    {
        application.setAppId(IdUtils.resourceId());
        application.setCreateTime(DateUtils.getNowDate());
        
        String securityLevel = calculateSecurityLevel(
            application.getUserType(),
            application.getAccessPath(),
            application.getDataSensitivity(),
            application.getBusinessImportance(),
            application.getSystemDependency()
        );
        application.setSecurityLevel(securityLevel);
        
        return applicationMapper.insertApplication(application);
    }

    /**
     * 修改应用
     *
     * @param application 应用
     * @return 结果
     */
    @Override
    public int updateApplication(Application application)
    {
        application.setUpdateTime(DateUtils.getNowDate());
        String securityLevel = calculateSecurityLevel(
                application.getUserType(),
                application.getAccessPath(),
                application.getDataSensitivity(),
                application.getBusinessImportance(),
                application.getSystemDependency()
        );
        application.setSecurityLevel(securityLevel);
        return applicationMapper.updateApplication(application);
    }

    /**
     * 批量删除应用
     *
     * @param appIds 需要删除的应用主键
     * @return 结果
     */
    @Override
    public int deleteApplicationByAppIds(Long[] appIds)
    {
        return applicationMapper.deleteApplicationByAppIds(appIds);
    }

    /**
     * 删除应用信息
     *
     * @param appId 应用主键
     * @return 结果
     */
    @Override
    public int deleteApplicationByAppId(Long appId)
    {
        return applicationMapper.deleteApplicationByAppId(appId);
    }

    /**
     * 计算系统安全等级
     * 
     * @param userType 用户类型（逗号分隔）
     * @param accessPath 访问路径（逗号分隔）
     * @param dataSensitivity 数据敏感度（逗号分隔）
     * @param businessImportance 业务重要性（逗号分隔）
     * @param systemDependency 系统依赖性（逗号分隔）
     * @return 安全等级
     */
    private String calculateSecurityLevel(String userType, String accessPath, String dataSensitivity, 
                                         String businessImportance, String systemDependency) {
        
        // 特殊规则：当数据敏感度是绝密数据时，直接定义为核心系统
        if (dataSensitivity != null && dataSensitivity.contains("绝密数据")) {
            return "核心系统";
        }
        
        int userTypeScore = getMaxUserTypeScore(userType);
        int accessPathScore = getMaxAccessPathScore(accessPath);
        int dataSensitivityScore = getMaxDataSensitivityScore(dataSensitivity);
        int businessImportanceScore = getMaxBusinessImportanceScore(businessImportance);
        int systemDependencyScore = getMaxSystemDependencyScore(systemDependency);
        
        double userTypeWeight = 0.1;
        double accessPathWeight = 0.15;
        double dataSensitivityWeight = 0.3;
        double businessImportanceWeight = 0.25;
        double systemDependencyWeight = 0.2;
        
        double totalOriginalWeight = 0;
        double weightedScore = 0;
        
        if (userType != null && !userType.trim().isEmpty()) {
            totalOriginalWeight += userTypeWeight;
        }
        if (accessPath != null && !accessPath.trim().isEmpty()) {
            totalOriginalWeight += accessPathWeight;
        }
        if (dataSensitivity != null && !dataSensitivity.trim().isEmpty()) {
            totalOriginalWeight += dataSensitivityWeight;
        }
        if (businessImportance != null && !businessImportance.trim().isEmpty()) {
            totalOriginalWeight += businessImportanceWeight;
        }
        if (systemDependency != null && !systemDependency.trim().isEmpty()) {
            totalOriginalWeight += systemDependencyWeight;
        }
        
        if (totalOriginalWeight == 0) {
            return "一般系统";
        }
        
        // 计算权重比例因子，将缺失维度的权重等比例分配给其他维度
        double weightFactor = 1.0 / totalOriginalWeight;
        
        if (userType != null && !userType.trim().isEmpty()) {
            weightedScore += userTypeScore * userTypeWeight * weightFactor;
        }
        if (accessPath != null && !accessPath.trim().isEmpty()) {
            weightedScore += accessPathScore * accessPathWeight * weightFactor;
        }
        if (dataSensitivity != null && !dataSensitivity.trim().isEmpty()) {
            weightedScore += dataSensitivityScore * dataSensitivityWeight * weightFactor;
        }
        if (businessImportance != null && !businessImportance.trim().isEmpty()) {
            weightedScore += businessImportanceScore * businessImportanceWeight * weightFactor;
        }
        if (systemDependency != null && !systemDependency.trim().isEmpty()) {
            weightedScore += systemDependencyScore * systemDependencyWeight * weightFactor;
        }
        
        if (weightedScore >= 8.5) {
            return "核心系统";
        } else if (weightedScore >= 5.5) {
            return "重要系统";
        } else {
            return "一般系统";
        }
    }
    
    private int getMaxUserTypeScore(String userType) {
        if (userType == null || userType.trim().isEmpty()) {
            return 0;
        }
        
        int maxScore = 0;
        String[] types = userType.split(",");
        for (String type : types) {
            type = type.trim();
            switch (type) {
                case "C端用户":
                    maxScore = Math.max(maxScore, 10);
                    break;
                case "第三方与合作伙伴":
                    maxScore = Math.max(maxScore, 7);
                    break;
                case "内部员工":
                    maxScore = Math.max(maxScore, 3);
                    break;
                case "其它系统":
                    maxScore = Math.max(maxScore, 1);
                    break;
            }
        }
        return maxScore;
    }
    
    private int getMaxAccessPathScore(String accessPath) {
        if (accessPath == null || accessPath.trim().isEmpty()) {
            return 0;
        }
        
        int maxScore = 0;
        String[] paths = accessPath.split(",");
        for (String path : paths) {
            path = path.trim();
            switch (path) {
                case "互联网":
                    maxScore = Math.max(maxScore, 10);
                    break;
                case "内网":
                    maxScore = Math.max(maxScore, 6);
                    break;
                case "私有局域网":
                    maxScore = Math.max(maxScore, 1);
                    break;
            }
        }
        return maxScore;
    }
    
    private int getMaxDataSensitivityScore(String dataSensitivity) {
        if (dataSensitivity == null || dataSensitivity.trim().isEmpty()) {
            return 0;
        }
        
        int maxScore = 0;
        String[] sensitivities = dataSensitivity.split(",");
        for (String sensitivity : sensitivities) {
            sensitivity = sensitivity.trim();
            switch (sensitivity) {
                case "绝密数据":
                    maxScore = Math.max(maxScore, 10);
                    break;
                case "机密数据":
                    maxScore = Math.max(maxScore, 7);
                    break;
                case "秘密数据":
                    maxScore = Math.max(maxScore, 5);
                    break;
                case "公开数据":
                    maxScore = Math.max(maxScore, 1);
                    break;
            }
        }
        return maxScore;
    }
    
    private int getMaxBusinessImportanceScore(String businessImportance) {
        if (businessImportance == null || businessImportance.trim().isEmpty()) {
            return 0;
        }
        
        int maxScore = 0;
        String[] importances = businessImportance.split(",");
        for (String importance : importances) {
            importance = importance.trim();
            switch (importance) {
                case "核心业务系统":
                    maxScore = Math.max(maxScore, 10);
                    break;
                case "关键支撑系统":
                    maxScore = Math.max(maxScore, 7);
                    break;
                case "一般业务系统":
                    maxScore = Math.max(maxScore, 3);
                    break;
                case "边缘支撑系统":
                    maxScore = Math.max(maxScore, 1);
                    break;
            }
        }
        return maxScore;
    }
    
    private int getMaxSystemDependencyScore(String systemDependency) {
        if (systemDependency == null || systemDependency.trim().isEmpty()) {
            return 0;
        }
        
        int maxScore = 0;
        String[] dependencies = systemDependency.split(",");
        for (String dependency : dependencies) {
            dependency = dependency.trim();
            switch (dependency) {
                case "核心依赖":
                    maxScore = Math.max(maxScore, 10);
                    break;
                case "关键依赖":
                    maxScore = Math.max(maxScore, 7);
                    break;
                case "一般依赖":
                    maxScore = Math.max(maxScore, 3);
                    break;
                case "独立系统":
                    maxScore = Math.max(maxScore, 1);
                    break;
            }
        }
        return maxScore;
    }

}
