package com.leapmotor.sdl.business.service;

import java.util.List;
import com.leapmotor.sdl.business.domain.Application;

/**
 * 应用Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-05
 */
public interface IApplicationService 
{
    /**
     * 查询应用
     * 
     * @param appId 应用主键
     * @return 应用
     */
    public Application selectApplicationByAppId(Long appId);

    /**
     * 查询应用列表
     * 
     * @param application 应用
     * @return 应用集合
     */
    public List<Application> selectApplicationList(Application application);

    /**
     * 新增应用
     * 
     * @param application 应用
     * @return 结果
     */
    public int insertApplication(Application application);

    /**
     * 修改应用
     * 
     * @param application 应用
     * @return 结果
     */
    public int updateApplication(Application application);

    /**
     * 批量删除应用
     * 
     * @param appIds 需要删除的应用主键集合
     * @return 结果
     */
    public int deleteApplicationByAppIds(Long[] appIds);

    /**
     * 删除应用信息
     * 
     * @param appId 应用主键
     * @return 结果
     */
    public int deleteApplicationByAppId(Long appId);
}
