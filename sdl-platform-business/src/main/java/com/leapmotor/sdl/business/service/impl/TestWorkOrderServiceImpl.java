package com.leapmotor.sdl.business.service.impl;

import java.util.List;
import com.leapmotor.sdl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.leapmotor.sdl.business.mapper.TestWorkOrderMapper;
import com.leapmotor.sdl.business.domain.TestWorkOrder;
import com.leapmotor.sdl.business.service.ITestWorkOrderService;

/**
 * 测试工单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@Service
public class TestWorkOrderServiceImpl implements ITestWorkOrderService 
{
    @Autowired
    private TestWorkOrderMapper testWorkOrderMapper;

    /**
     * 查询测试工单
     * 
     * @param ticketId 测试工单主键
     * @return 测试工单
     */
    @Override
    public TestWorkOrder selectTestWorkOrderByTicketId(Long ticketId)
    {
        return testWorkOrderMapper.selectTestWorkOrderByTicketId(ticketId);
    }

    /**
     * 查询测试工单列表
     * 
     * @param testWorkOrder 测试工单
     * @return 测试工单
     */
    @Override
    public List<TestWorkOrder> selectTestWorkOrderList(TestWorkOrder testWorkOrder)
    {
        return testWorkOrderMapper.selectTestWorkOrderList(testWorkOrder);
    }

    /**
     * 新增测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    @Override
    public int insertTestWorkOrder(TestWorkOrder testWorkOrder)
    {
        testWorkOrder.setCreateTime(DateUtils.getNowDate());
        return testWorkOrderMapper.insertTestWorkOrder(testWorkOrder);
    }

    /**
     * 修改测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    @Override
    public int updateTestWorkOrder(TestWorkOrder testWorkOrder)
    {
        testWorkOrder.setUpdateTime(DateUtils.getNowDate());
        return testWorkOrderMapper.updateTestWorkOrder(testWorkOrder);
    }

    /**
     * 批量删除测试工单
     * 
     * @param ticketIds 需要删除的测试工单主键
     * @return 结果
     */
    @Override
    public int deleteTestWorkOrderByTicketIds(Long[] ticketIds)
    {
        return testWorkOrderMapper.deleteTestWorkOrderByTicketIds(ticketIds);
    }

    /**
     * 删除测试工单信息
     * 
     * @param ticketId 测试工单主键
     * @return 结果
     */
    @Override
    public int deleteTestWorkOrderByTicketId(Long ticketId)
    {
        return testWorkOrderMapper.deleteTestWorkOrderByTicketId(ticketId);
    }
}
