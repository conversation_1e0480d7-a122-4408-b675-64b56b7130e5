package com.leapmotor.sdl.business.service;

import java.util.List;
import com.leapmotor.sdl.business.domain.TestWorkOrder;

/**
 * 测试工单Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
public interface ITestWorkOrderService 
{
    /**
     * 查询测试工单
     * 
     * @param ticketId 测试工单主键
     * @return 测试工单
     */
    public TestWorkOrder selectTestWorkOrderByTicketId(Long ticketId);

    /**
     * 查询测试工单列表
     * 
     * @param testWorkOrder 测试工单
     * @return 测试工单集合
     */
    public List<TestWorkOrder> selectTestWorkOrderList(TestWorkOrder testWorkOrder);

    /**
     * 新增测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    public int insertTestWorkOrder(TestWorkOrder testWorkOrder);

    /**
     * 修改测试工单
     * 
     * @param testWorkOrder 测试工单
     * @return 结果
     */
    public int updateTestWorkOrder(TestWorkOrder testWorkOrder);

    /**
     * 批量删除测试工单
     * 
     * @param ticketIds 需要删除的测试工单主键集合
     * @return 结果
     */
    public int deleteTestWorkOrderByTicketIds(Long[] ticketIds);

    /**
     * 删除测试工单信息
     * 
     * @param ticketId 测试工单主键
     * @return 结果
     */
    public int deleteTestWorkOrderByTicketId(Long ticketId);
}
