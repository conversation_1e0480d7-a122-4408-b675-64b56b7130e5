package com.leapmotor.sdl.business.controller;

import java.util.List;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.leapmotor.sdl.common.annotation.Log;
import com.leapmotor.sdl.common.core.controller.BaseController;
import com.leapmotor.sdl.common.core.domain.AjaxResult;
import com.leapmotor.sdl.common.enums.BusinessType;
import com.leapmotor.sdl.business.domain.TestWorkOrder;
import com.leapmotor.sdl.business.service.ITestWorkOrderService;
import com.leapmotor.sdl.common.utils.poi.ExcelUtil;
import com.leapmotor.sdl.common.core.page.TableDataInfo;

/**
 * 测试工单Controller
 * 
 * <AUTHOR>
 * @date 2025-07-13
 */
@RestController
@RequestMapping("/business/workorder")
public class TestWorkOrderController extends BaseController
{
    @Autowired
    private ITestWorkOrderService testWorkOrderService;

    /**
     * 查询测试工单列表
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:list')")
    @GetMapping("/list")
    public TableDataInfo list(TestWorkOrder testWorkOrder)
    {
        startPage();
        List<TestWorkOrder> list = testWorkOrderService.selectTestWorkOrderList(testWorkOrder);
        return getDataTable(list);
    }

    /**
     * 导出测试工单列表
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:export')")
    @Log(title = "测试工单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TestWorkOrder testWorkOrder)
    {
        List<TestWorkOrder> list = testWorkOrderService.selectTestWorkOrderList(testWorkOrder);
        ExcelUtil<TestWorkOrder> util = new ExcelUtil<TestWorkOrder>(TestWorkOrder.class);
        util.exportExcel(response, list, "测试工单数据");
    }

    /**
     * 获取测试工单详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:query')")
    @GetMapping(value = "/{ticketId}")
    public AjaxResult getInfo(@PathVariable("ticketId") Long ticketId)
    {
        return success(testWorkOrderService.selectTestWorkOrderByTicketId(ticketId));
    }

    /**
     * 新增测试工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:add')")
    @Log(title = "测试工单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TestWorkOrder testWorkOrder)
    {
        return toAjax(testWorkOrderService.insertTestWorkOrder(testWorkOrder));
    }

    /**
     * 修改测试工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:edit')")
    @Log(title = "测试工单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TestWorkOrder testWorkOrder)
    {
        return toAjax(testWorkOrderService.updateTestWorkOrder(testWorkOrder));
    }

    /**
     * 删除测试工单
     */
    @PreAuthorize("@ss.hasPermi('business:workorder:remove')")
    @Log(title = "测试工单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ticketIds}")
    public AjaxResult remove(@PathVariable Long[] ticketIds)
    {
        return toAjax(testWorkOrderService.deleteTestWorkOrderByTicketIds(ticketIds));
    }
}
