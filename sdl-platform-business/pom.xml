<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>sdl-platform</artifactId>
        <groupId>com.leapmotor.sdl</groupId>
        <version>3.9.0</version>
    </parent>
    <version>3.9.0</version>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>sdl-platform-business</artifactId>

    <description>
        SDL平台核心业务功能模块
    </description>

    <dependencies>

        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
        </dependency>
        <!-- 核心模块 -->
        <dependency>
            <groupId>com.leapmotor.sdl</groupId>
            <artifactId>sdl-platform-framework</artifactId>
        </dependency>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.leapmotor.sdl</groupId>
            <artifactId>sdl-platform-common</artifactId>
        </dependency>


    </dependencies>

</project>