# SDL Platform Docker 构建忽略文件
# 用于减少Docker构建上下文大小，提高构建速度

# Git 相关
.git
.gitignore
.gitattributes
.github

# IDE 相关
.idea
.vscode
*.iml
*.iws
*.ipr
.settings
.project
.classpath
.factorypath

# 构建产物
target/
dist/
build/
out/

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn
.pnpm-debug.log*
.pnpm-store/

# 前端缓存
.cache/
.vite/
.nuxt/
.next/
.svelte-kit/

# 日志文件
*.log
logs/

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# 测试相关
coverage/
.nyc_output/
junit.xml
test-results/

# 文档
README.md
DOCKER_DEPLOYMENT.md
docs/
*.md

# 脚本文件（不需要在容器中）
*.sh
*.bat
*.cmd

# Docker 相关（避免递归）
Dockerfile*
docker-compose*.yml
.dockerignore

# 环境配置
.env*
!.env.example

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 证书文件
*.pem
*.key
*.crt
*.p12
*.jks

# 其他
.claude
CLAUDE.md
