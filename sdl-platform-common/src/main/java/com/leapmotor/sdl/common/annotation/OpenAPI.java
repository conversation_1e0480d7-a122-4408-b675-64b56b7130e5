package com.leapmotor.sdl.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * OpenAPI接口标识注解
 * 
 * 用于标识Controller类提供OpenAPI访问能力
 * 被此注解标注的Controller将支持通过API Key进行访问
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface OpenAPI 
{
    /**
     * 权限组标识
     * 
     * 用于权限分组管理，不同的API Key可以配置不同的权限组访问权限
     *
     * @return 权限组名称，默认为"default"
     */
    String group() default "default";
    
    /**
     * 接口描述
     * 
     * 用于生成OpenAPI文档时的接口描述信息
     * 
     * @return 接口描述信息
     */
    String description() default "";
}