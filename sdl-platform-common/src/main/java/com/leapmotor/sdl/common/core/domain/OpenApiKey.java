package com.leapmotor.sdl.common.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.leapmotor.sdl.common.annotation.Excel;
import com.leapmotor.sdl.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * OpenAPI密钥管理对象 openapi_key
 * 
 * 用于管理OpenAPI访问密钥，包含权限组、IP白名单、限流等配置
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public class OpenApiKey extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** API密钥，64位随机字符串 */
    @Excel(name = "API密钥")
    private String apiKey;

    /** 密钥名称，便于管理识别 */
    @Excel(name = "密钥名称")
    private String name;

    /** 密钥状态（0正常 1停用） */
    @Excel(name = "密钥状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 允许访问的权限组，多个用逗号分隔 */
    @Excel(name = "权限组")
    private String allowedGroups;

    /** IP白名单，多个IP或IP段用逗号分隔 */
    @Excel(name = "IP白名单")
    private String ipWhitelist;

    /** 每小时请求限制次数 */
    @Excel(name = "限流次数")
    private Integer rateLimit;

    /** 密钥过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 最后访问时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastAccessTime;

    /** 总访问次数 */
    private Long totalAccessCount;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setApiKey(String apiKey) 
    {
        this.apiKey = apiKey;
    }

    public String getApiKey() 
    {
        return apiKey;
    }
    
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    
    public void setAllowedGroups(String allowedGroups) 
    {
        this.allowedGroups = allowedGroups;
    }

    public String getAllowedGroups() 
    {
        return allowedGroups;
    }
    
    public void setIpWhitelist(String ipWhitelist) 
    {
        this.ipWhitelist = ipWhitelist;
    }

    public String getIpWhitelist() 
    {
        return ipWhitelist;
    }
    
    public void setRateLimit(Integer rateLimit) 
    {
        this.rateLimit = rateLimit;
    }

    public Integer getRateLimit() 
    {
        return rateLimit;
    }
    
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    
    public void setLastAccessTime(Date lastAccessTime) 
    {
        this.lastAccessTime = lastAccessTime;
    }

    public Date getLastAccessTime() 
    {
        return lastAccessTime;
    }
    
    public void setTotalAccessCount(Long totalAccessCount) 
    {
        this.totalAccessCount = totalAccessCount;
    }

    public Long getTotalAccessCount() 
    {
        return totalAccessCount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("apiKey", getApiKey())
            .append("name", getName())
            .append("status", getStatus())
            .append("allowedGroups", getAllowedGroups())
            .append("ipWhitelist", getIpWhitelist())
            .append("rateLimit", getRateLimit())
            .append("expireTime", getExpireTime())
            .append("lastAccessTime", getLastAccessTime())
            .append("totalAccessCount", getTotalAccessCount())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}