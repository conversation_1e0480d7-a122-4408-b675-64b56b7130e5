package com.leapmotor.sdl.common.utils.openapi;

import com.leapmotor.sdl.common.utils.StringUtils;
import com.leapmotor.sdl.common.utils.ip.IpUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * OpenAPI工具类
 * 
 * 提供OpenAPI相关的工具方法，包括IP白名单验证、权限组验证等
 * 
 * <AUTHOR>
 */
public class OpenApiUtils 
{
    private static final Logger log = LoggerFactory.getLogger(OpenApiUtils.class);
    
    /** IP地址正则表达式 */
    private static final String IP_REGEX = "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$";
    
    /** CIDR正则表达式 */
    private static final String CIDR_REGEX = "^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])/(([0-9]|[1-2][0-9]|3[0-2]))$";
    
    /**
     * 生成API Key
     * 
     * 生成64位随机字符串作为API Key
     * 
     * @return 生成的API Key
     */
    public static String generateApiKey() 
    {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder();
        java.util.Random random = new java.util.Random();
        
        for (int i = 0; i < 64; i++) 
        {
            sb.append(chars.charAt(random.nextInt(chars.length())));
        }
        
        return sb.toString();
    }
    
    /**
     * 验证IP是否在白名单中
     * 
     * 支持单个IP地址和CIDR格式的IP段
     * 
     * @param clientIp 客户端IP
     * @param ipWhitelist IP白名单，多个IP用逗号分隔
     * @return 是否在白名单中
     */
    public static boolean isIpInWhitelist(String clientIp, String ipWhitelist) 
    {
        // 如果白名单为空，则允许所有IP
        if (StringUtils.isEmpty(ipWhitelist)) 
        {
            return true;
        }
        
        // 如果客户端IP为空，则拒绝访问
        if (StringUtils.isEmpty(clientIp)) 
        {
            return false;
        }
        
        // 分割IP白名单
        String[] ips = ipWhitelist.split(",");
        
        for (String ip : ips) 
        {
            ip = ip.trim();
            
            // 检查是否为CIDR格式
            if (ip.contains("/")) 
            {
                if (isValidCidr(ip) && isIpInCidr(clientIp, ip)) 
                {
                    return true;
                }
            } 
            else 
            {
                // 单个IP地址匹配
                if (isValidIp(ip) && clientIp.equals(ip)) 
                {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 验证权限组是否允许访问
     * 
     * @param requestGroup 请求的权限组
     * @param allowedGroups 允许的权限组，多个用逗号分隔
     * @return 是否允许访问
     */
    public static boolean isGroupAllowed(String requestGroup, String allowedGroups) 
    {
        // 如果允许的权限组为空，则拒绝访问
        if (StringUtils.isEmpty(allowedGroups)) 
        {
            return false;
        }
        
        // 如果请求的权限组为空，使用默认组
        if (StringUtils.isEmpty(requestGroup)) 
        {
            requestGroup = "default";
        }
        
        // 分割权限组
        String[] groups = allowedGroups.split(",");
        
        return Arrays.asList(groups).contains(requestGroup.trim());
    }
    
    /**
     * 验证IP地址格式是否正确
     * 
     * @param ip IP地址
     * @return 是否为有效的IP地址
     */
    private static boolean isValidIp(String ip) 
    {
        return Pattern.matches(IP_REGEX, ip);
    }
    
    /**
     * 验证CIDR格式是否正确
     * 
     * @param cidr CIDR格式的IP段
     * @return 是否为有效的CIDR格式
     */
    private static boolean isValidCidr(String cidr) 
    {
        return Pattern.matches(CIDR_REGEX, cidr);
    }
    
    /**
     * 检查IP是否在CIDR范围内
     * 
     * @param ip 要检查的IP
     * @param cidr CIDR格式的IP段
     * @return 是否在范围内
     */
    private static boolean isIpInCidr(String ip, String cidr) 
    {
        try 
        {
            String[] parts = cidr.split("/");
            String networkIp = parts[0];
            int prefixLength = Integer.parseInt(parts[1]);
            
            long ipLong = ipToLong(ip);
            long networkLong = ipToLong(networkIp);
            
            // 计算网络掩码
            long mask = (0xFFFFFFFFL << (32 - prefixLength)) & 0xFFFFFFFFL;
            
            return (ipLong & mask) == (networkLong & mask);
        } 
        catch (Exception e) 
        {
            log.error("检查IP是否在CIDR范围内时发生错误: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 将IP地址转换为长整型
     * 
     * @param ip IP地址
     * @return 长整型值
     */
    private static long ipToLong(String ip) 
    {
        String[] parts = ip.split("\\.");
        long result = 0;
        
        for (int i = 0; i < 4; i++) 
        {
            result += Long.parseLong(parts[i]) << (8 * (3 - i));
        }
        
        return result;
    }
    
    /**
     * 获取Redis缓存key
     * 
     * @param apiKey API密钥
     * @return Redis缓存key
     */
    public static String getCacheKey(String apiKey) 
    {
        return "openapi:key:" + apiKey;
    }
    
    /**
     * 获取限流Redis key
     * 
     * @param apiKey API密钥
     * @return 限流Redis key
     */
    public static String getRateLimitKey(String apiKey) 
    {
        return "openapi:rate:limit:" + apiKey;
    }
}