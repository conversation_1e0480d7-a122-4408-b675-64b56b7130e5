package com.leapmotor.sdl.common.core.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.leapmotor.sdl.common.annotation.Excel;
import com.leapmotor.sdl.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * OpenAPI访问日志对象 openapi_access_log
 * 
 * 用于记录OpenAPI接口的访问日志，包含请求信息、响应状态、耗时等
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public class OpenApiAccessLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** API密钥 */
    @Excel(name = "API密钥")
    private String apiKey;

    /** 请求URI */
    @Excel(name = "请求URI")
    private String requestUri;

    /** 请求方法 */
    @Excel(name = "请求方法")
    private String requestMethod;

    /** 客户端IP */
    @Excel(name = "客户端IP")
    private String clientIp;

    /** 用户代理 */
    @Excel(name = "用户代理")
    private String userAgent;

    /** 响应状态码 */
    @Excel(name = "响应状态码")
    private Integer responseStatus;

    /** 响应时间（毫秒） */
    @Excel(name = "响应时间")
    private Long responseTime;

    /** 请求时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "请求时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

    /** 错误信息 */
    private String errorMessage;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setApiKey(String apiKey) 
    {
        this.apiKey = apiKey;
    }

    public String getApiKey() 
    {
        return apiKey;
    }
    
    public void setRequestUri(String requestUri) 
    {
        this.requestUri = requestUri;
    }

    public String getRequestUri() 
    {
        return requestUri;
    }
    
    public void setRequestMethod(String requestMethod) 
    {
        this.requestMethod = requestMethod;
    }

    public String getRequestMethod() 
    {
        return requestMethod;
    }
    
    public void setClientIp(String clientIp) 
    {
        this.clientIp = clientIp;
    }

    public String getClientIp() 
    {
        return clientIp;
    }
    
    public void setUserAgent(String userAgent) 
    {
        this.userAgent = userAgent;
    }

    public String getUserAgent() 
    {
        return userAgent;
    }
    
    public void setResponseStatus(Integer responseStatus) 
    {
        this.responseStatus = responseStatus;
    }

    public Integer getResponseStatus() 
    {
        return responseStatus;
    }
    
    public void setResponseTime(Long responseTime) 
    {
        this.responseTime = responseTime;
    }

    public Long getResponseTime() 
    {
        return responseTime;
    }
    
    public void setRequestTime(Date requestTime) 
    {
        this.requestTime = requestTime;
    }

    public Date getRequestTime() 
    {
        return requestTime;
    }
    
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("apiKey", getApiKey())
            .append("requestUri", getRequestUri())
            .append("requestMethod", getRequestMethod())
            .append("clientIp", getClientIp())
            .append("userAgent", getUserAgent())
            .append("responseStatus", getResponseStatus())
            .append("responseTime", getResponseTime())
            .append("requestTime", getRequestTime())
            .append("errorMessage", getErrorMessage())
            .toString();
    }
}